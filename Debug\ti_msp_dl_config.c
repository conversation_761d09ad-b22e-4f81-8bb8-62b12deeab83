/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.c =============
 *  Configured MSPM0 DriverLib module definitions
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */

#include "ti_msp_dl_config.h"

DL_TimerA_backupConfig gWHEELBackup;

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform any initialization needed before using any board APIs
 */
SYSCONFIG_WEAK void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_GPIO_init();
    /* Module-Specific Initializations*/
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_WHEEL_init();
    SYSCFG_DL_WTKJ_init();
    SYSCFG_DL_XUNJI_init();
    /* Ensure backup structures have no valid state */
	gWHEELBackup.backupRdy 	= false;


}
/*
 * User should take care to save and restore register configuration in application.
 * See Retention Configuration section for more details.
 */
SYSCONFIG_WEAK bool SYSCFG_DL_saveConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerA_saveConfiguration(WHEEL_INST, &gWHEELBackup);

    return retStatus;
}


SYSCONFIG_WEAK bool SYSCFG_DL_restoreConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerA_restoreConfiguration(WHEEL_INST, &gWHEELBackup, false);

    return retStatus;
}

SYSCONFIG_WEAK void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_TimerA_reset(WHEEL_INST);
    DL_UART_Main_reset(WTKJ_INST);
    DL_UART_Main_reset(XUNJI_INST);

    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_TimerA_enablePower(WHEEL_INST);
    DL_UART_Main_enablePower(WTKJ_INST);
    DL_UART_Main_enablePower(XUNJI_INST);
    delay_cycles(POWER_STARTUP_DELAY);
}

SYSCONFIG_WEAK void SYSCFG_DL_GPIO_init(void)
{

    DL_GPIO_initPeripheralOutputFunction(GPIO_WHEEL_C0_IOMUX,GPIO_WHEEL_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_WHEEL_C0_PORT, GPIO_WHEEL_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_WHEEL_C1_IOMUX,GPIO_WHEEL_C1_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_WHEEL_C1_PORT, GPIO_WHEEL_C1_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_WHEEL_C2_IOMUX,GPIO_WHEEL_C2_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_WHEEL_C2_PORT, GPIO_WHEEL_C2_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_WHEEL_C3_IOMUX,GPIO_WHEEL_C3_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_WHEEL_C3_PORT, GPIO_WHEEL_C3_PIN);

    DL_GPIO_initPeripheralOutputFunction(
        GPIO_WTKJ_IOMUX_TX, GPIO_WTKJ_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_WTKJ_IOMUX_RX, GPIO_WTKJ_IOMUX_RX_FUNC);
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_XUNJI_IOMUX_TX, GPIO_XUNJI_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_XUNJI_IOMUX_RX, GPIO_XUNJI_IOMUX_RX_FUNC);

    DL_GPIO_initDigitalOutput(OLED_SCL_IOMUX);

    DL_GPIO_initDigitalOutput(OLED_SDA_IOMUX);

    DL_GPIO_initDigitalOutput(LEBEEP_LED_IOMUX);

    DL_GPIO_initDigitalOutput(LEBEEP_BEEP_IOMUX);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY1_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY2_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY3_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalInputFeatures(KEY_KEY4_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_initDigitalOutput(Direction_PWM11_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM12_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM21_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM22_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM31_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM32_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM41_IOMUX);

    DL_GPIO_initDigitalOutput(Direction_PWM42_IOMUX);

    DL_GPIO_clearPins(GPIOA, Direction_PWM11_PIN);
    DL_GPIO_setPins(GPIOA, LEBEEP_LED_PIN |
		LEBEEP_BEEP_PIN |
		Direction_PWM12_PIN);
    DL_GPIO_enableOutput(GPIOA, LEBEEP_LED_PIN |
		LEBEEP_BEEP_PIN |
		Direction_PWM11_PIN |
		Direction_PWM12_PIN);
    DL_GPIO_clearPins(GPIOB, Direction_PWM21_PIN |
		Direction_PWM32_PIN |
		Direction_PWM41_PIN |
		Direction_PWM42_PIN);
    DL_GPIO_setPins(GPIOB, OLED_SCL_PIN |
		OLED_SDA_PIN |
		Direction_PWM22_PIN |
		Direction_PWM31_PIN);
    DL_GPIO_enableOutput(GPIOB, OLED_SCL_PIN |
		OLED_SDA_PIN |
		Direction_PWM21_PIN |
		Direction_PWM22_PIN |
		Direction_PWM31_PIN |
		Direction_PWM32_PIN |
		Direction_PWM41_PIN |
		Direction_PWM42_PIN);

}


SYSCONFIG_WEAK void SYSCFG_DL_SYSCTL_init(void)
{

	//Low Power Mode is configured to be SLEEP0
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);

    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    /* Set default configuration */
    DL_SYSCTL_disableHFXT();
    DL_SYSCTL_disableSYSPLL();
    DL_SYSCTL_setULPCLKDivider(DL_SYSCTL_ULPCLK_DIV_1);
    DL_SYSCTL_setMCLKDivider(DL_SYSCTL_MCLK_DIVIDER_DISABLE);

}


/*
 * Timer clock configuration to be sourced by  / 1 (32000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   32000000 Hz = 32000000 Hz / (1 * (0 + 1))
 */
static const DL_TimerA_ClockConfig gWHEELClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale = 0U
};

static const DL_TimerA_PWMConfig gWHEELConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN_UP,
    .period = 1000,
    .isTimerWithFourCC = true,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_WHEEL_init(void) {

    DL_TimerA_setClockConfig(
        WHEEL_INST, (DL_TimerA_ClockConfig *) &gWHEELClockConfig);

    DL_TimerA_initPWMMode(
        WHEEL_INST, (DL_TimerA_PWMConfig *) &gWHEELConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerA_setCounterControl(WHEEL_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerA_setCaptureCompareOutCtl(WHEEL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_0_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(WHEEL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_0_INDEX);
    DL_TimerA_setCaptureCompareValue(WHEEL_INST, 0, DL_TIMER_CC_0_INDEX);

    DL_TimerA_setCaptureCompareOutCtl(WHEEL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_1_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(WHEEL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_1_INDEX);
    DL_TimerA_setCaptureCompareValue(WHEEL_INST, 0, DL_TIMER_CC_1_INDEX);

    DL_TimerA_setCaptureCompareOutCtl(WHEEL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_2_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(WHEEL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_2_INDEX);
    DL_TimerA_setCaptureCompareValue(WHEEL_INST, 0, DL_TIMER_CC_2_INDEX);

    DL_TimerA_setCaptureCompareOutCtl(WHEEL_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERA_CAPTURE_COMPARE_3_INDEX);

    DL_TimerA_setCaptCompUpdateMethod(WHEEL_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERA_CAPTURE_COMPARE_3_INDEX);
    DL_TimerA_setCaptureCompareValue(WHEEL_INST, 0, DL_TIMER_CC_3_INDEX);

    DL_TimerA_enableClock(WHEEL_INST);


    
    DL_TimerA_setCCPDirection(WHEEL_INST , DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT | DL_TIMER_CC2_OUTPUT | DL_TIMER_CC3_OUTPUT );


}


static const DL_UART_Main_ClockConfig gWTKJClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gWTKJConfig = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_WTKJ_init(void)
{
    DL_UART_Main_setClockConfig(WTKJ_INST, (DL_UART_Main_ClockConfig *) &gWTKJClockConfig);

    DL_UART_Main_init(WTKJ_INST, (DL_UART_Main_Config *) &gWTKJConfig);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 115200
     *  Actual baud rate: 115211.52
     */
    DL_UART_Main_setOversampling(WTKJ_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(WTKJ_INST, WTKJ_IBRD_32_MHZ_115200_BAUD, WTKJ_FBRD_32_MHZ_115200_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(WTKJ_INST,
                                 DL_UART_MAIN_INTERRUPT_RX);


    DL_UART_Main_enable(WTKJ_INST);
}
static const DL_UART_Main_ClockConfig gXUNJIClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gXUNJIConfig = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_XUNJI_init(void)
{
    DL_UART_Main_setClockConfig(XUNJI_INST, (DL_UART_Main_ClockConfig *) &gXUNJIClockConfig);

    DL_UART_Main_init(XUNJI_INST, (DL_UART_Main_Config *) &gXUNJIConfig);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 115200
     *  Actual baud rate: 115211.52
     */
    DL_UART_Main_setOversampling(XUNJI_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(XUNJI_INST, XUNJI_IBRD_32_MHZ_115200_BAUD, XUNJI_FBRD_32_MHZ_115200_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(XUNJI_INST,
                                 DL_UART_MAIN_INTERRUPT_RX);


    DL_UART_Main_enable(XUNJI_INST);
}

