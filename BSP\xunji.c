#include "xunji.h"

static uint8_t  i = 0, data[10]={0};
static uint8_t buffer[14];

static void uart_send_byte(uint8_t byte)
{
    while (DL_UART_isBusy(XUNJI_UARTx) == true)
        ;
    DL_UART_Main_transmitData(XUNJI_UARTx, byte);
}

static void uart_send_string(const char *str)
{
    while (*str)
    {
        uart_send_byte(*str++);
    }
}

void XUNJI_init()
{
    NVIC_ClearPendingIRQ(XUNJI_INT_IRQn);
    NVIC_EnableIRQ(XUNJI_INT_IRQn);
     uart_send_string("xunji is all ready\r\n");
}



uint8_t xunji_read(uint8_t c)
{
    if (c > 10)
        return 0;
    else
        return data[c];
}

uint8_t buffer_re(uint8_t k)
{
    return buffer[k];
}

static void XUNJI_handle()
{
    uint8_t k;
    buffer[i++] = DL_UART_Main_receiveData(XUNJI_UARTx);
    data[10]++;
   // uart_send_string("y");
   
    if ((i == 1) && ((buffer[0] != 'a')))
    {
        i = 0;
    }
    if ((i == 2) && ((buffer[1] != 'b')))
    {
        i = 0;
    }

    if (i == 13) 
    {
    if( buffer[12] == '-')
    {
        for (k = 0; k < 10; k++)
        {
            data[k] = buffer[k + 2] - '0';
        }
            for (k = 0; k < 14; k++)
              buffer[k] = 0;
    }
     i=0;
    }

}

void XUNJI_IRQHandler(void)
{
    switch (DL_UART_getPendingInterrupt(XUNJI_UARTx))
    {
    case DL_UART_IIDX_RX:
        XUNJI_handle();
        break;
    }
}
