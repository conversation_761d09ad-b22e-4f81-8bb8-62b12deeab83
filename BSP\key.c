#include "key.h"
#include "delay.h"

static uint8_t KEY1_STATE()
{
    if (DL_GPIO_readPins(KEY_KEY1_PORT, KEY_KEY1_PIN))
        return 1;
    else
        return 0;
}

static uint8_t KEY2_STATE()
{
    if (DL_GPIO_readPins(KEY_KEY2_PORT, KEY_KEY2_PIN))
        return 1;
    else
        return 0;
}

static uint8_t KEY3_STATE()
{
    if (DL_GPIO_readPins(KEY_KEY4_PORT, KEY_KEY4_PIN))
        return 1;
    else
        return 0;
}

static uint8_t KEY4_STATE()
{
    if (DL_GPIO_readPins(KEY_KEY3_PORT, KEY_KEY3_PIN))
        return 1;
    else
        return 0;
}

uint8_t key_read(void)
{
    uint8_t key = 0;
    if (KEY1_STATE() == KEY_BUTTON)
    {
        delay_ms(20);
        if (KEY1_STATE() == KEY_BUTTON)
        {
            while (KEY1_STATE() == KEY_BUTTON)
                ;
            key = 1;
        }
        delay_ms(20);
    }

    if (KEY2_STATE() == KEY_BUTTON)
    {
        delay_ms(20);
        if (KEY2_STATE() == KEY_BUTTON)
        {
            while (KEY2_STATE() == KEY_BUTTON)
                ;
            key = 2;
        }
        delay_ms(20);
    }

    if (KEY3_STATE() == KEY_BUTTON)
    {
        delay_ms(20);
        if (KEY3_STATE() == KEY_BUTTON)
        {
            while (KEY3_STATE() == KEY_BUTTON)
                ;
            key = 3;
        }
        delay_ms(20);
    }

    if (KEY4_STATE() == KEY_BUTTON)
    {
        delay_ms(20);
        if (KEY4_STATE() == KEY_BUTTON)
        {
            while (KEY4_STATE() == KEY_BUTTON)
                ;
            key = 4;
        }
        delay_ms(20);
    }
    return key;
}
