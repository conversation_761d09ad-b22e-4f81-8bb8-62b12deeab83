#include "WTKJ.h"

static float xyz[3];
static int16_t buff[8];
static uint8_t i = 0;
void WTKJ_init()
{
    NVIC_ClearPendingIRQ(WTKJ_INT_IRQn);
    NVIC_EnableIRQ(WTKJ_INT_IRQn); 
}

static void WTKJ_handle()
{
    buff[i++] = DL_UART_Main_receiveData(WTKJ_UARTx);
    if ((i == 1) && (buff[0] != 0x55))
    {
        i = 0;
    }
    if ((i == 2) && (buff[1] != 0x53))
    {
        i = 0;
    }
    if (i == 8)
    {
        int16_t value_x = (buff[3] << 8) | buff[2];
        int16_t value_y = (buff[5] << 8) | buff[4];
        int16_t value_z = (buff[7] << 8) | buff[6];
        xyz[0] = (float)value_x / 32768 * 180;
        xyz[1] = (float)value_y / 32768 * 180;
        xyz[2] = (float)value_z / 32768 * 180;

        for (int j = 0; j < 8; j++)
        {
            buff[j] = 0;
        }
        i = 0;
    }
}

float wtjk_getxyz(uint8_t index)
{
    return xyz[index];
}

void WTKJ_IRQHandler(void)
{
    switch (DL_UART_getPendingInterrupt(WTKJ_UARTx))
    {
    case DL_UART_IIDX_RX:
        WTKJ_handle();
        break;
    }
}