<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o diansai.out -mdiansai.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iD:/TI/project/diansai -iD:/TI/project/diansai/Debug/syscfg -iD:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=diansai_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./BSP/OLED.o ./BSP/WTKJ.o ./BSP/delay.o ./BSP/key.o ./BSP/led.o ./BSP/wheel.o ./BSP/xunji.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x68851bb0</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\TI\project\diansai\Debug\diansai.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1c99</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\TI\project\diansai\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\TI\project\diansai\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\TI\project\diansai\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>OLED.o</file>
         <name>OLED.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>WTKJ.o</file>
         <name>WTKJ.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>led.o</file>
         <name>led.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>wheel.o</file>
         <name>wheel.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>D:\TI\project\diansai\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>xunji.o</file>
         <name>xunji.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\project\diansai\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-18">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-1a">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e4">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-e5">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-e6">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e7">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e8">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e9">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-ea">
         <path>D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.wheel_rotate</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x174</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x234</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x234</run_address>
         <size>0x154</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.wheel_go_back</name>
         <load_address>0x388</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x388</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.wheel_go_left</name>
         <load_address>0x4d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4d0</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.wheel_go_forward</name>
         <load_address>0x618</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x618</run_address>
         <size>0x144</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-162">
         <name>.text.wheel_go_right</name>
         <load_address>0x75c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x75c</run_address>
         <size>0x144</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.tb6612_set_direction</name>
         <load_address>0x8a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8a0</run_address>
         <size>0x130</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text.WTKJ_handle</name>
         <load_address>0x9d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d0</run_address>
         <size>0x128</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.wheel_polarity_test</name>
         <load_address>0xaf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf8</run_address>
         <size>0x11e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xc16</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc16</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc18</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.XUNJI_handle</name>
         <load_address>0xd1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd1c</run_address>
         <size>0xe0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.SYSCFG_DL_WHEEL_init</name>
         <load_address>0xdfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdfc</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-168">
         <name>.text</name>
         <load_address>0xed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed4</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.OLED_Init</name>
         <load_address>0xfac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfac</run_address>
         <size>0xce</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text.__mulsf3</name>
         <load_address>0x107c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x107c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.count_save</name>
         <load_address>0x1108</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1108</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.__divsf3</name>
         <load_address>0x1190</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1190</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1214</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1214</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1290</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1290</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.OLED_Clear</name>
         <load_address>0x12f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12f8</run_address>
         <size>0x56</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-163">
         <name>.text.__aeabi_idivmod</name>
         <load_address>0x1350</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1350</run_address>
         <size>0x56</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-167">
         <name>.text.calculate_angle_diff</name>
         <load_address>0x13a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a6</run_address>
         <size>0x56</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.OLED_I2C_SendByte</name>
         <load_address>0x13fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13fc</run_address>
         <size>0x54</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.set_all_pwm_channels_separate</name>
         <load_address>0x1450</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1450</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x14a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14a4</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-170">
         <name>.text.__fixdfsi</name>
         <load_address>0x14f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14f0</run_address>
         <size>0x4a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_UART_init</name>
         <load_address>0x153c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x153c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.SYSCFG_DL_WTKJ_init</name>
         <load_address>0x1584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1584</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.SYSCFG_DL_XUNJI_init</name>
         <load_address>0x15cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15cc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.set_all_pwm_channels</name>
         <load_address>0x1614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1614</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.wheel_get_count</name>
         <load_address>0x1658</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1658</run_address>
         <size>0x42</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.__extendsfdf2</name>
         <load_address>0x169c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x169c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-75">
         <name>.text.main</name>
         <load_address>0x16dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16dc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.wheel_go_back_count_close</name>
         <load_address>0x171c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x171c</run_address>
         <size>0x40</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.wheel_go_forward_count_close</name>
         <load_address>0x175c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x175c</run_address>
         <size>0x40</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.wheel_go_left_count_close</name>
         <load_address>0x179c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x179c</run_address>
         <size>0x40</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.wheel_go_right_count_close</name>
         <load_address>0x17dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17dc</run_address>
         <size>0x40</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x181c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x181c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.__floatsisf</name>
         <load_address>0x1858</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1858</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1894</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1894</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.__muldsi3</name>
         <load_address>0x18d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d0</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.wheel_go_left_openloop</name>
         <load_address>0x190a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x190a</run_address>
         <size>0x3a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.wheel_go_right_openloop</name>
         <load_address>0x1944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1944</run_address>
         <size>0x3a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.wheel_rotate_left_openloop</name>
         <load_address>0x197e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x197e</run_address>
         <size>0x3a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text.wheel_rotate_right_openloop</name>
         <load_address>0x19b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19b8</run_address>
         <size>0x3a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.__fixsfsi</name>
         <load_address>0x19f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19f4</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.wheel_go_back_openloop</name>
         <load_address>0x1a2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a2c</run_address>
         <size>0x38</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.wheel_go_forward_openloop</name>
         <load_address>0x1a64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a64</run_address>
         <size>0x38</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.OLED_SetCursor</name>
         <load_address>0x1a9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a9c</run_address>
         <size>0x36</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1ad4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-153">
         <name>.text.OLED_W_SCL</name>
         <load_address>0x1b08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b08</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.OLED_W_SDA</name>
         <load_address>0x1b3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b3c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1b70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b70</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1b9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b9c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.OLED_WriteCommand</name>
         <load_address>0x1bc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bc8</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.OLED_WriteData</name>
         <load_address>0x1bf2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bf2</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1c1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c1c</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1c46</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c46</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1c70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c70</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1c98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c98</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.OLED_I2C_Start</name>
         <load_address>0x1cc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cc0</run_address>
         <size>0x24</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1ce4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ce4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.LED_OFF</name>
         <load_address>0x1d04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d04</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1d24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d24</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1d44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d44</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x1d60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d60</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1d7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d7c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1d98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d98</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1db4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1db4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1dd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dd0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1dec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dec</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.OLED_I2C_Stop</name>
         <load_address>0x1e08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e08</run_address>
         <size>0x1c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x1e24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e24</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.UART1_IRQHandler</name>
         <load_address>0x1e40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e40</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1e5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e5c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1e74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e74</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1e8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e8c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1ea4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1ebc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ebc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1ed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ed4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1eec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eec</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1f04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f04</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1f1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f1c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1f34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f34</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-123">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1f4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f4c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1f64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f64</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1f7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f7c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1f94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f94</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.text.wtjk_getxyz</name>
         <load_address>0x1fac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fac</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1fc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fc4</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.OLED_I2C_Init</name>
         <load_address>0x1fda</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fda</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.WTKJ_init</name>
         <load_address>0x1ff0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ff0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.delay_ms</name>
         <load_address>0x2006</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2006</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-50">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x201c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x201c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2032</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2032</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2046</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2046</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x205a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x205a</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x206e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x206e</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x2084</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2084</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2098</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2098</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x20ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20ac</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x20c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x20d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20d4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x20e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20e8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x20fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20fa</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x210c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x210c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x211e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x211e</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x2130</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2130</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x2144</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2144</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x2154</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2154</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.get_angle_value</name>
         <load_address>0x2164</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2164</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2172</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2172</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.text.wheel_begin_go</name>
         <load_address>0x217c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x217c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.wheel_init</name>
         <load_address>0x2186</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2186</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.wheel_stop</name>
         <load_address>0x2190</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2190</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-49">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x219c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x219c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text:abort</name>
         <load_address>0x21a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21a4</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x21aa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21aa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.HOSTexit</name>
         <load_address>0x21ae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21ae</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x21b2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21b2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text._system_pre_init</name>
         <load_address>0x21b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21b6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>__TI_handler_table</name>
         <load_address>0x21e8</load_address>
         <readonly>true</readonly>
         <run_address>0x21e8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1df">
         <name>.cinit..bss.load</name>
         <load_address>0x21f4</load_address>
         <readonly>true</readonly>
         <run_address>0x21f4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1de">
         <name>.cinit..data.load</name>
         <load_address>0x21fc</load_address>
         <readonly>true</readonly>
         <run_address>0x21fc</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1dd">
         <name>__TI_cinit_table</name>
         <load_address>0x2204</load_address>
         <readonly>true</readonly>
         <run_address>0x2204</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-150">
         <name>.rodata.gWTKJConfig</name>
         <load_address>0x21c0</load_address>
         <readonly>true</readonly>
         <run_address>0x21c0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.rodata.gXUNJIConfig</name>
         <load_address>0x21ca</load_address>
         <readonly>true</readonly>
         <run_address>0x21ca</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-143">
         <name>.rodata.gWHEELConfig</name>
         <load_address>0x21d4</load_address>
         <readonly>true</readonly>
         <run_address>0x21d4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-142">
         <name>.rodata.gWHEELClockConfig</name>
         <load_address>0x21dc</load_address>
         <readonly>true</readonly>
         <run_address>0x21dc</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.rodata.gWTKJClockConfig</name>
         <load_address>0x21df</load_address>
         <readonly>true</readonly>
         <run_address>0x21df</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.rodata.gXUNJIClockConfig</name>
         <load_address>0x21e1</load_address>
         <readonly>true</readonly>
         <run_address>0x21e1</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-90">
         <name>.data.i</name>
         <load_address>0x20200102</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200102</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-198">
         <name>.data.count_save.count</name>
         <load_address>0x202000e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000e8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-96">
         <name>.data.data</name>
         <load_address>0x202000f8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f8</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-94">
         <name>.data.i</name>
         <load_address>0x20200103</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200103</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-92">
         <name>.bss.xyz</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000dc</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.bss.buff</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-95">
         <name>.bss.buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000cc</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.common:gWHEELBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0x7e</load_address>
         <run_address>0x7e</run_address>
         <size>0x1d9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_abbrev</name>
         <load_address>0x257</load_address>
         <run_address>0x257</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_abbrev</name>
         <load_address>0x2c4</load_address>
         <run_address>0x2c4</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x42c</load_address>
         <run_address>0x42c</run_address>
         <size>0x1c9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_abbrev</name>
         <load_address>0x5f5</load_address>
         <run_address>0x5f5</run_address>
         <size>0x61</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x656</load_address>
         <run_address>0x656</run_address>
         <size>0xe2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_abbrev</name>
         <load_address>0x738</load_address>
         <run_address>0x738</run_address>
         <size>0x16f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0x8a7</load_address>
         <run_address>0x8a7</run_address>
         <size>0x1cd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_abbrev</name>
         <load_address>0xa74</load_address>
         <run_address>0xa74</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_abbrev</name>
         <load_address>0xad6</load_address>
         <run_address>0xad6</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_abbrev</name>
         <load_address>0xd5c</load_address>
         <run_address>0xd5c</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xff7</load_address>
         <run_address>0xff7</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_abbrev</name>
         <load_address>0x10a6</load_address>
         <run_address>0x10a6</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_abbrev</name>
         <load_address>0x1216</load_address>
         <run_address>0x1216</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_abbrev</name>
         <load_address>0x124f</load_address>
         <run_address>0x124f</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_abbrev</name>
         <load_address>0x1311</load_address>
         <run_address>0x1311</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0x1381</load_address>
         <run_address>0x1381</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_abbrev</name>
         <load_address>0x140e</load_address>
         <run_address>0x140e</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_abbrev</name>
         <load_address>0x14a6</load_address>
         <run_address>0x14a6</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_abbrev</name>
         <load_address>0x14d2</load_address>
         <run_address>0x14d2</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_abbrev</name>
         <load_address>0x14f9</load_address>
         <run_address>0x14f9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_abbrev</name>
         <load_address>0x1520</load_address>
         <run_address>0x1520</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_abbrev</name>
         <load_address>0x1547</load_address>
         <run_address>0x1547</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-197">
         <name>.debug_abbrev</name>
         <load_address>0x156e</load_address>
         <run_address>0x156e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-199">
         <name>.debug_abbrev</name>
         <load_address>0x1595</load_address>
         <run_address>0x1595</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0x15bc</load_address>
         <run_address>0x15bc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-194">
         <name>.debug_abbrev</name>
         <load_address>0x15e3</load_address>
         <run_address>0x15e3</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_abbrev</name>
         <load_address>0x160a</load_address>
         <run_address>0x160a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_abbrev</name>
         <load_address>0x1631</load_address>
         <run_address>0x1631</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_abbrev</name>
         <load_address>0x1658</load_address>
         <run_address>0x1658</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x16b1</load_address>
         <run_address>0x16b1</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_abbrev</name>
         <load_address>0x16d6</load_address>
         <run_address>0x16d6</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_info</name>
         <load_address>0xf8</load_address>
         <run_address>0xf8</run_address>
         <size>0x29e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2adc</load_address>
         <run_address>0x2adc</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_info</name>
         <load_address>0x2b5c</load_address>
         <run_address>0x2b5c</run_address>
         <size>0xe58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x39b4</load_address>
         <run_address>0x39b4</run_address>
         <size>0x95b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_info</name>
         <load_address>0x430f</load_address>
         <run_address>0x430f</run_address>
         <size>0x9e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_info</name>
         <load_address>0x43ad</load_address>
         <run_address>0x43ad</run_address>
         <size>0x7b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0x4b65</load_address>
         <run_address>0x4b65</run_address>
         <size>0x1e61</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_info</name>
         <load_address>0x69c6</load_address>
         <run_address>0x69c6</run_address>
         <size>0xa19</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_info</name>
         <load_address>0x73df</load_address>
         <run_address>0x73df</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_info</name>
         <load_address>0x7454</load_address>
         <run_address>0x7454</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_info</name>
         <load_address>0xa5c6</load_address>
         <run_address>0xa5c6</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xb86c</load_address>
         <run_address>0xb86c</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_info</name>
         <load_address>0xbc8f</load_address>
         <run_address>0xbc8f</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0xc3d3</load_address>
         <run_address>0xc3d3</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_info</name>
         <load_address>0xc419</load_address>
         <run_address>0xc419</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0xc5ab</load_address>
         <run_address>0xc5ab</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xc671</load_address>
         <run_address>0xc671</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_info</name>
         <load_address>0xc7ed</load_address>
         <run_address>0xc7ed</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_info</name>
         <load_address>0xc8e5</load_address>
         <run_address>0xc8e5</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_info</name>
         <load_address>0xc920</load_address>
         <run_address>0xc920</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0xcac7</load_address>
         <run_address>0xcac7</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0xcc54</load_address>
         <run_address>0xcc54</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_info</name>
         <load_address>0xcde1</load_address>
         <run_address>0xcde1</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_info</name>
         <load_address>0xcf78</load_address>
         <run_address>0xcf78</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_info</name>
         <load_address>0xd107</load_address>
         <run_address>0xd107</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0xd296</load_address>
         <run_address>0xd296</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_info</name>
         <load_address>0xd429</load_address>
         <run_address>0xd429</run_address>
         <size>0x1b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0xd5e2</load_address>
         <run_address>0xd5e2</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_info</name>
         <load_address>0xd77b</load_address>
         <run_address>0xd77b</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_info</name>
         <load_address>0xd90a</load_address>
         <run_address>0xd90a</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0xd98f</load_address>
         <run_address>0xd98f</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_info</name>
         <load_address>0xdc89</load_address>
         <run_address>0xdc89</run_address>
         <size>0x8f</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x155</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_str</name>
         <load_address>0x155</load_address>
         <run_address>0x155</run_address>
         <size>0x22f7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_str</name>
         <load_address>0x244c</load_address>
         <run_address>0x244c</run_address>
         <size>0x146</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_str</name>
         <load_address>0x2592</load_address>
         <run_address>0x2592</run_address>
         <size>0x5de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x2b70</load_address>
         <run_address>0x2b70</run_address>
         <size>0x809</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_str</name>
         <load_address>0x3379</load_address>
         <run_address>0x3379</run_address>
         <size>0xf2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x346b</load_address>
         <run_address>0x346b</run_address>
         <size>0x467</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_str</name>
         <load_address>0x38d2</load_address>
         <run_address>0x38d2</run_address>
         <size>0xa87</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_str</name>
         <load_address>0x4359</load_address>
         <run_address>0x4359</run_address>
         <size>0x836</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_str</name>
         <load_address>0x4b8f</load_address>
         <run_address>0x4b8f</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_str</name>
         <load_address>0x4cfc</load_address>
         <run_address>0x4cfc</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_str</name>
         <load_address>0x6ac8</load_address>
         <run_address>0x6ac8</run_address>
         <size>0xce3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x77ab</load_address>
         <run_address>0x77ab</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_str</name>
         <load_address>0x79d0</load_address>
         <run_address>0x79d0</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_str</name>
         <load_address>0x7cff</load_address>
         <run_address>0x7cff</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_str</name>
         <load_address>0x7df4</load_address>
         <run_address>0x7df4</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_str</name>
         <load_address>0x7f8f</load_address>
         <run_address>0x7f8f</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_str</name>
         <load_address>0x80f7</load_address>
         <run_address>0x80f7</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_str</name>
         <load_address>0x82cc</load_address>
         <run_address>0x82cc</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_str</name>
         <load_address>0x8414</load_address>
         <run_address>0x8414</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_str</name>
         <load_address>0x84fd</load_address>
         <run_address>0x84fd</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_frame</name>
         <load_address>0x2c</load_address>
         <run_address>0x2c</run_address>
         <size>0x354</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x380</load_address>
         <run_address>0x380</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_frame</name>
         <load_address>0x3b0</load_address>
         <run_address>0x3b0</run_address>
         <size>0x228</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x5d8</load_address>
         <run_address>0x5d8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_frame</name>
         <load_address>0x69c</load_address>
         <run_address>0x69c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_frame</name>
         <load_address>0x6e4</load_address>
         <run_address>0x6e4</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_frame</name>
         <load_address>0x784</load_address>
         <run_address>0x784</run_address>
         <size>0x3a4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_frame</name>
         <load_address>0xb28</load_address>
         <run_address>0xb28</run_address>
         <size>0x138</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_frame</name>
         <load_address>0xc60</load_address>
         <run_address>0xc60</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_frame</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_frame</name>
         <load_address>0x1088</load_address>
         <run_address>0x1088</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0x1240</load_address>
         <run_address>0x1240</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_frame</name>
         <load_address>0x12d0</load_address>
         <run_address>0x12d0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_frame</name>
         <load_address>0x13d0</load_address>
         <run_address>0x13d0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_frame</name>
         <load_address>0x13f0</load_address>
         <run_address>0x13f0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x1428</load_address>
         <run_address>0x1428</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x1450</load_address>
         <run_address>0x1450</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_frame</name>
         <load_address>0x1480</load_address>
         <run_address>0x1480</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_frame</name>
         <load_address>0x14b0</load_address>
         <run_address>0x14b0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_frame</name>
         <load_address>0x14d0</load_address>
         <run_address>0x14d0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_line</name>
         <load_address>0xf9</load_address>
         <run_address>0xf9</run_address>
         <size>0x956</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0xa4f</load_address>
         <run_address>0xa4f</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_line</name>
         <load_address>0xb07</load_address>
         <run_address>0xb07</run_address>
         <size>0x8fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x1402</load_address>
         <run_address>0x1402</run_address>
         <size>0x3ed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_line</name>
         <load_address>0x17ef</load_address>
         <run_address>0x17ef</run_address>
         <size>0xf0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x18df</load_address>
         <run_address>0x18df</run_address>
         <size>0x25d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_line</name>
         <load_address>0x1b3c</load_address>
         <run_address>0x1b3c</run_address>
         <size>0xec5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_line</name>
         <load_address>0x2a01</load_address>
         <run_address>0x2a01</run_address>
         <size>0x4c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_line</name>
         <load_address>0x2ec2</load_address>
         <run_address>0x2ec2</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0x303a</load_address>
         <run_address>0x303a</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_line</name>
         <load_address>0x47a8</load_address>
         <run_address>0x47a8</run_address>
         <size>0xa17</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x51bf</load_address>
         <run_address>0x51bf</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x539b</load_address>
         <run_address>0x539b</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_line</name>
         <load_address>0x58b5</load_address>
         <run_address>0x58b5</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_line</name>
         <load_address>0x58f3</load_address>
         <run_address>0x58f3</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x59f1</load_address>
         <run_address>0x59f1</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x5ab1</load_address>
         <run_address>0x5ab1</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_line</name>
         <load_address>0x5c79</load_address>
         <run_address>0x5c79</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x5ce0</load_address>
         <run_address>0x5ce0</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.debug_line</name>
         <load_address>0x5d21</load_address>
         <run_address>0x5d21</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0x5e28</load_address>
         <run_address>0x5e28</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_line</name>
         <load_address>0x5f08</load_address>
         <run_address>0x5f08</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_line</name>
         <load_address>0x5fe4</load_address>
         <run_address>0x5fe4</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_line</name>
         <load_address>0x60a4</load_address>
         <run_address>0x60a4</run_address>
         <size>0xc1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_line</name>
         <load_address>0x6165</load_address>
         <run_address>0x6165</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0x621d</load_address>
         <run_address>0x621d</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_line</name>
         <load_address>0x62d9</load_address>
         <run_address>0x62d9</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x63a5</load_address>
         <run_address>0x63a5</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_line</name>
         <load_address>0x6449</load_address>
         <run_address>0x6449</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_line</name>
         <load_address>0x6502</load_address>
         <run_address>0x6502</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x65b7</load_address>
         <run_address>0x65b7</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x128</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_ranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_ranges</name>
         <load_address>0x230</load_address>
         <run_address>0x230</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_ranges</name>
         <load_address>0x248</load_address>
         <run_address>0x248</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_ranges</name>
         <load_address>0x280</load_address>
         <run_address>0x280</run_address>
         <size>0x120</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_ranges</name>
         <load_address>0x410</load_address>
         <run_address>0x410</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_ranges</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x790</load_address>
         <run_address>0x790</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_ranges</name>
         <load_address>0x7d8</load_address>
         <run_address>0x7d8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_ranges</name>
         <load_address>0x820</load_address>
         <run_address>0x820</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_ranges</name>
         <load_address>0x838</load_address>
         <run_address>0x838</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_ranges</name>
         <load_address>0x888</load_address>
         <run_address>0x888</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-191">
         <name>.debug_ranges</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_ranges</name>
         <load_address>0x8b8</load_address>
         <run_address>0x8b8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_loc</name>
         <load_address>0x1a3a</load_address>
         <run_address>0x1a3a</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_loc</name>
         <load_address>0x21f6</load_address>
         <run_address>0x21f6</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_loc</name>
         <load_address>0x22ce</load_address>
         <run_address>0x22ce</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_loc</name>
         <load_address>0x26f2</load_address>
         <run_address>0x26f2</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x285e</load_address>
         <run_address>0x285e</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x28cd</load_address>
         <run_address>0x28cd</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_loc</name>
         <load_address>0x2a34</load_address>
         <run_address>0x2a34</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_loc</name>
         <load_address>0x2a5a</load_address>
         <run_address>0x2a5a</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e8"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2100</size>
         <contents>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-71"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x21e8</load_address>
         <run_address>0x21e8</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1dd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x21c0</load_address>
         <run_address>0x21c0</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-151"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000e8</run_address>
         <size>0x1c</size>
         <contents>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xe8</size>
         <contents>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-ec"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1e1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-19d" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-19e" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-19f" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a0" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a1" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a2" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a4" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c0" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x16e5</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-1e3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c2" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdd18</size>
         <contents>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-1e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c4" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8690</size>
         <contents>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-19b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c6" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1500</size>
         <contents>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-192"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c8" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6657</size>
         <contents>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ca" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8e0</size>
         <contents>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1cc" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2a7a</size>
         <contents>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-19c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1d6" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x168</size>
         <contents>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-9a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1e0" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1f3" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2218</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1f4" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x104</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1f5" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x2218</used_space>
         <unused_space>0x1dde8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2100</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x21c0</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x21e8</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2218</start_address>
               <size>0x1dde8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x304</used_space>
         <unused_space>0x7cfc</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1a2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1a4"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xe8</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000e8</start_address>
               <size>0x1c</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200104</start_address>
               <size>0x7cfc</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x21f4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xe8</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x21fc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x202000e8</run_address>
            <run_size>0x1c</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2204</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2214</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2214</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x21e8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x21f4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3b">
         <name>main</name>
         <value>0x16dd</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-c9">
         <name>SYSCFG_DL_init</name>
         <value>0x1c71</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-ca">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1291</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-cb">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x235</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-cc">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1c1d</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-cd">
         <name>SYSCFG_DL_WHEEL_init</name>
         <value>0xdfd</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-ce">
         <name>SYSCFG_DL_WTKJ_init</name>
         <value>0x1585</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-cf">
         <name>SYSCFG_DL_XUNJI_init</name>
         <value>0x15cd</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-d0">
         <name>gWHEELBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-db">
         <name>Default_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-dc">
         <name>Reset_Handler</name>
         <value>0x21b3</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-dd">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-de">
         <name>NMI_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-df">
         <name>HardFault_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e0">
         <name>SVC_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e1">
         <name>PendSV_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e2">
         <name>SysTick_Handler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e3">
         <name>GROUP0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e4">
         <name>GROUP1_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e5">
         <name>TIMG8_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e6">
         <name>UART3_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e7">
         <name>ADC0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e8">
         <name>ADC1_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e9">
         <name>CANFD0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ea">
         <name>DAC0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-eb">
         <name>SPI0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ec">
         <name>SPI1_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ed">
         <name>UART2_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ee">
         <name>TIMG0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ef">
         <name>TIMG6_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f0">
         <name>TIMA0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f1">
         <name>TIMA1_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f2">
         <name>TIMG7_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f3">
         <name>TIMG12_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f4">
         <name>I2C0_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f5">
         <name>I2C1_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f6">
         <name>AES_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f7">
         <name>RTC_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f8">
         <name>DMA_IRQHandler</name>
         <value>0x21ab</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-120">
         <name>OLED_I2C_Init</name>
         <value>0x1fdb</value>
         <object_component_ref idref="oc-f5"/>
      </symbol>
      <symbol id="sm-121">
         <name>OLED_I2C_Start</name>
         <value>0x1cc1</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-122">
         <name>OLED_I2C_Stop</name>
         <value>0x1e09</value>
         <object_component_ref idref="oc-157"/>
      </symbol>
      <symbol id="sm-123">
         <name>OLED_I2C_SendByte</name>
         <value>0x13fd</value>
         <object_component_ref idref="oc-156"/>
      </symbol>
      <symbol id="sm-124">
         <name>OLED_WriteCommand</name>
         <value>0x1bc9</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-125">
         <name>OLED_WriteData</name>
         <value>0x1bf3</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-126">
         <name>OLED_SetCursor</name>
         <value>0x1a9d</value>
         <object_component_ref idref="oc-158"/>
      </symbol>
      <symbol id="sm-127">
         <name>OLED_Clear</name>
         <value>0x12f9</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-128">
         <name>OLED_Init</name>
         <value>0xfad</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-150">
         <name>WTKJ_init</name>
         <value>0x1ff1</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-151">
         <name>wtjk_getxyz</name>
         <value>0x1fad</value>
         <object_component_ref idref="oc-18a"/>
      </symbol>
      <symbol id="sm-152">
         <name>UART1_IRQHandler</name>
         <value>0x1e41</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-15b">
         <name>delay_ms</name>
         <value>0x2007</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-16d">
         <name>LED_OFF</name>
         <value>0x1d05</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>set_all_pwm_channels</name>
         <value>0x1615</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>set_all_pwm_channels_separate</name>
         <value>0x1451</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-1be">
         <name>tb6612_set_direction</name>
         <value>0x8a1</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-1bf">
         <name>wheel_init</name>
         <value>0x2187</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>wheel_stop</name>
         <value>0x2191</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>wheel_begin_go</name>
         <value>0x217d</value>
         <object_component_ref idref="oc-15c"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>count_save</name>
         <value>0x1109</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>wheel_get_count</name>
         <value>0x1659</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>get_angle_value</name>
         <value>0x2165</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-1c5">
         <name>calculate_angle_diff</name>
         <value>0x13a7</value>
         <object_component_ref idref="oc-167"/>
      </symbol>
      <symbol id="sm-1c6">
         <name>wheel_go_back</name>
         <value>0x389</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>wheel_go_forward</name>
         <value>0x619</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>wheel_go_left</name>
         <value>0x4d1</value>
         <object_component_ref idref="oc-161"/>
      </symbol>
      <symbol id="sm-1c9">
         <name>wheel_go_right</name>
         <value>0x75d</value>
         <object_component_ref idref="oc-162"/>
      </symbol>
      <symbol id="sm-1ca">
         <name>wheel_rotate</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>wheel_go_forward_openloop</name>
         <value>0x1a65</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>wheel_go_back_openloop</name>
         <value>0x1a2d</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>wheel_go_left_openloop</name>
         <value>0x190b</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>wheel_go_right_openloop</name>
         <value>0x1945</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-1cf">
         <name>wheel_rotate_left_openloop</name>
         <value>0x197f</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-1d0">
         <name>wheel_rotate_right_openloop</name>
         <value>0x19b9</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-1d1">
         <name>wheel_go_forward_count_close</name>
         <value>0x175d</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-1d2">
         <name>wheel_go_back_count_close</name>
         <value>0x171d</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-1d3">
         <name>wheel_go_left_count_close</name>
         <value>0x179d</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>wheel_go_right_count_close</name>
         <value>0x17dd</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>wheel_polarity_test</name>
         <value>0xaf9</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>UART0_IRQHandler</name>
         <value>0x1e25</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f2">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f3">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f4">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f5">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f6">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f7">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f8">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f9">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-202">
         <name>DL_Common_delayCycles</name>
         <value>0x2173</value>
         <object_component_ref idref="oc-10a"/>
      </symbol>
      <symbol id="sm-219">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1dd1</value>
         <object_component_ref idref="oc-135"/>
      </symbol>
      <symbol id="sm-21a">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x2155</value>
         <object_component_ref idref="oc-13f"/>
      </symbol>
      <symbol id="sm-21b">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1db5</value>
         <object_component_ref idref="oc-13e"/>
      </symbol>
      <symbol id="sm-21c">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1f65</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-21d">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc19</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-22a">
         <name>DL_UART_init</name>
         <value>0x153d</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-22b">
         <name>DL_UART_setClockConfig</name>
         <value>0x210d</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-236">
         <name>_c_int00_noargs</name>
         <value>0x1c99</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-237">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-243">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1895</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-24b">
         <name>_system_pre_init</name>
         <value>0x21b7</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-256">
         <name>__TI_zero_init_nomemset</name>
         <value>0x201d</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-25f">
         <name>__TI_decompress_none</name>
         <value>0x2131</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-26a">
         <name>__TI_decompress_lzss</name>
         <value>0x1215</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-274">
         <name>abort</name>
         <value>0x21a5</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-27e">
         <name>HOSTexit</name>
         <value>0x21af</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-27f">
         <name>C$$EXIT</name>
         <value>0x21ae</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-285">
         <name>__aeabi_fadd</name>
         <value>0xedf</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-286">
         <name>__addsf3</name>
         <value>0xedf</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-287">
         <name>__aeabi_fsub</name>
         <value>0xed5</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-288">
         <name>__subsf3</name>
         <value>0xed5</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-28e">
         <name>__aeabi_fmul</name>
         <value>0x107d</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-28f">
         <name>__mulsf3</name>
         <value>0x107d</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-295">
         <name>__aeabi_fdiv</name>
         <value>0x1191</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-296">
         <name>__divsf3</name>
         <value>0x1191</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-29c">
         <name>__aeabi_f2d</name>
         <value>0x169d</value>
         <object_component_ref idref="oc-16c"/>
      </symbol>
      <symbol id="sm-29d">
         <name>__extendsfdf2</name>
         <value>0x169d</value>
         <object_component_ref idref="oc-16c"/>
      </symbol>
      <symbol id="sm-2a3">
         <name>__aeabi_d2iz</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-170"/>
      </symbol>
      <symbol id="sm-2a4">
         <name>__fixdfsi</name>
         <value>0x14f1</value>
         <object_component_ref idref="oc-170"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>__aeabi_f2iz</name>
         <value>0x19f5</value>
         <object_component_ref idref="oc-18b"/>
      </symbol>
      <symbol id="sm-2ab">
         <name>__fixsfsi</name>
         <value>0x19f5</value>
         <object_component_ref idref="oc-18b"/>
      </symbol>
      <symbol id="sm-2b1">
         <name>__aeabi_i2f</name>
         <value>0x1859</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-2b2">
         <name>__floatsisf</name>
         <value>0x1859</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-2b8">
         <name>__aeabi_idiv</name>
         <value>0x1351</value>
         <object_component_ref idref="oc-163"/>
      </symbol>
      <symbol id="sm-2b9">
         <name>__aeabi_idivmod</name>
         <value>0x1351</value>
         <object_component_ref idref="oc-163"/>
      </symbol>
      <symbol id="sm-2bf">
         <name>__aeabi_memcpy</name>
         <value>0x219d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2c0">
         <name>__aeabi_memcpy4</name>
         <value>0x219d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2c1">
         <name>__aeabi_memcpy8</name>
         <value>0x219d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2c7">
         <name>__muldsi3</name>
         <value>0x18d1</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-2d1">
         <name>__aeabi_idiv0</name>
         <value>0xc17</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-2da">
         <name>TI_memcpy_small</name>
         <value>0x211f</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-2db">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2de">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2df">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
