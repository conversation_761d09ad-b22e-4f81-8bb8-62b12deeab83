#include "led.h"

void LED_ON(void)
{
 
    DL_GPIO_clearPins(LEBEEP_PORT , LEBEEP_LED_PIN);
     DL_GPIO_setPins(LEBEEP_PORT , LEBEEP_BEEP_PIN);
}

void LED_OFF(void)
{
      DL_GPIO_setPins(LEBEEP_PORT , LEBEEP_LED_PIN);
     DL_GPIO_clearPins(LEBEEP_PORT , LEBEEP_BEEP_PIN);
}

void LED_TOGGLE(void)
{
     DL_GPIO_togglePins(LEBEEP_PORT , LEBEEP_LED_PIN);
      DL_GPIO_togglePins(LEBEEP_PORT , LEBEEP_BEEP_PIN);
    }


