******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sun Jul 27 02:17:20 2025

OUTPUT FILE NAME:   <diansai.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001c99


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002218  0001dde8  R  X
  SRAM                  20200000   00008000  00000304  00007cfc  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002218   00002218    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002100   00002100    r-x .text
  000021c0    000021c0    00000028   00000028    r-- .rodata
  000021e8    000021e8    00000030   00000030    r-- .cinit
20200000    20200000    00000104   00000000    rw-
  20200000    20200000    000000e8   00000000    rw- .bss
  202000e8    202000e8    0000001c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002100     
                  000000c0    00000174     wheel.o (.text.wheel_rotate)
                  00000234    00000154     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000388    00000148     wheel.o (.text.wheel_go_back)
                  000004d0    00000148     wheel.o (.text.wheel_go_left)
                  00000618    00000144     wheel.o (.text.wheel_go_forward)
                  0000075c    00000144     wheel.o (.text.wheel_go_right)
                  000008a0    00000130     wheel.o (.text.tb6612_set_direction)
                  000009d0    00000128     WTKJ.o (.text.WTKJ_handle)
                  00000af8    0000011e     wheel.o (.text.wheel_polarity_test)
                  00000c16    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000c18    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000d1c    000000e0     xunji.o (.text.XUNJI_handle)
                  00000dfc    000000d8     ti_msp_dl_config.o (.text.SYSCFG_DL_WHEEL_init)
                  00000ed4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000fac    000000ce     OLED.o (.text.OLED_Init)
                  0000107a    00000002     --HOLE-- [fill = 0]
                  0000107c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001108    00000088     wheel.o (.text.count_save)
                  00001190    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00001212    00000002     --HOLE-- [fill = 0]
                  00001214    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001290    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000012f8    00000056     OLED.o (.text.OLED_Clear)
                  0000134e    00000002     --HOLE-- [fill = 0]
                  00001350    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000013a6    00000056     wheel.o (.text.calculate_angle_diff)
                  000013fc    00000054     OLED.o (.text.OLED_I2C_SendByte)
                  00001450    00000054     wheel.o (.text.set_all_pwm_channels_separate)
                  000014a4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000014f0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000153a    00000002     --HOLE-- [fill = 0]
                  0000153c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001584    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_WTKJ_init)
                  000015cc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_XUNJI_init)
                  00001614    00000044     wheel.o (.text.set_all_pwm_channels)
                  00001658    00000042     wheel.o (.text.wheel_get_count)
                  0000169a    00000002     --HOLE-- [fill = 0]
                  0000169c    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000016dc    00000040     empty.o (.text.main)
                  0000171c    00000040     wheel.o (.text.wheel_go_back_count_close)
                  0000175c    00000040     wheel.o (.text.wheel_go_forward_count_close)
                  0000179c    00000040     wheel.o (.text.wheel_go_left_count_close)
                  000017dc    00000040     wheel.o (.text.wheel_go_right_count_close)
                  0000181c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001858    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001894    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000018d0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000190a    0000003a     wheel.o (.text.wheel_go_left_openloop)
                  00001944    0000003a     wheel.o (.text.wheel_go_right_openloop)
                  0000197e    0000003a     wheel.o (.text.wheel_rotate_left_openloop)
                  000019b8    0000003a     wheel.o (.text.wheel_rotate_right_openloop)
                  000019f2    00000002     --HOLE-- [fill = 0]
                  000019f4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00001a2c    00000038     wheel.o (.text.wheel_go_back_openloop)
                  00001a64    00000038     wheel.o (.text.wheel_go_forward_openloop)
                  00001a9c    00000036     OLED.o (.text.OLED_SetCursor)
                  00001ad2    00000002     --HOLE-- [fill = 0]
                  00001ad4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001b08    00000034     OLED.o (.text.OLED_W_SCL)
                  00001b3c    00000034     OLED.o (.text.OLED_W_SDA)
                  00001b70    0000002c     WTKJ.o (.text.__NVIC_ClearPendingIRQ)
                  00001b9c    0000002c     WTKJ.o (.text.__NVIC_EnableIRQ)
                  00001bc8    0000002a     OLED.o (.text.OLED_WriteCommand)
                  00001bf2    0000002a     OLED.o (.text.OLED_WriteData)
                  00001c1c    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001c46    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001c6e    00000002     --HOLE-- [fill = 0]
                  00001c70    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001c98    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001cc0    00000024     OLED.o (.text.OLED_I2C_Start)
                  00001ce4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001d04    00000020     led.o (.text.LED_OFF)
                  00001d24    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001d42    00000002     --HOLE-- [fill = 0]
                  00001d44    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001d60    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00001d7c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001d98    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001db4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001dd0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001dec    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001e08    0000001c     OLED.o (.text.OLED_I2C_Stop)
                  00001e24    0000001c     xunji.o (.text.UART0_IRQHandler)
                  00001e40    0000001c     WTKJ.o (.text.UART1_IRQHandler)
                  00001e5c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001e74    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001e8c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001ea4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001ebc    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00001ed4    00000018     led.o (.text.DL_GPIO_setPins)
                  00001eec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001f04    00000018     wheel.o (.text.DL_GPIO_setPins)
                  00001f1c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001f34    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001f4c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001f64    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001f7c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001f94    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001fac    00000018     WTKJ.o (.text.wtjk_getxyz)
                  00001fc4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001fda    00000016     OLED.o (.text.OLED_I2C_Init)
                  00001ff0    00000016     WTKJ.o (.text.WTKJ_init)
                  00002006    00000016     delay.o (.text.delay_ms)
                  0000201c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002032    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00002046    00000014     led.o (.text.DL_GPIO_clearPins)
                  0000205a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000206e    00000014     wheel.o (.text.DL_GPIO_clearPins)
                  00002082    00000002     --HOLE-- [fill = 0]
                  00002084    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002098    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000020ac    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000020c0    00000014     WTKJ.o (.text.DL_UART_receiveData)
                  000020d4    00000014     xunji.o (.text.DL_UART_receiveData)
                  000020e8    00000012     WTKJ.o (.text.DL_UART_getPendingInterrupt)
                  000020fa    00000012     xunji.o (.text.DL_UART_getPendingInterrupt)
                  0000210c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000211e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002130    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002142    00000002     --HOLE-- [fill = 0]
                  00002144    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00002154    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002164    0000000e     wheel.o (.text.get_angle_value)
                  00002172    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000217c    0000000a     wheel.o (.text.wheel_begin_go)
                  00002186    0000000a     wheel.o (.text.wheel_init)
                  00002190    0000000a     wheel.o (.text.wheel_stop)
                  0000219a    00000002     --HOLE-- [fill = 0]
                  0000219c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000021a4    00000006     libc.a : exit.c.obj (.text:abort)
                  000021aa    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000021ae    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000021b2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000021b6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000021ba    00000006     --HOLE-- [fill = 0]

.cinit     0    000021e8    00000030     
                  000021e8    0000000c     (__TI_handler_table)
                  000021f4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000021fc    00000008     (.cinit..data.load) [load image, compression = lzss]
                  00002204    00000010     (__TI_cinit_table)
                  00002214    00000004     --HOLE-- [fill = 0]

.rodata    0    000021c0    00000028     
                  000021c0    0000000a     ti_msp_dl_config.o (.rodata.gWTKJConfig)
                  000021ca    0000000a     ti_msp_dl_config.o (.rodata.gXUNJIConfig)
                  000021d4    00000008     ti_msp_dl_config.o (.rodata.gWHEELConfig)
                  000021dc    00000003     ti_msp_dl_config.o (.rodata.gWHEELClockConfig)
                  000021df    00000002     ti_msp_dl_config.o (.rodata.gWTKJClockConfig)
                  000021e1    00000002     ti_msp_dl_config.o (.rodata.gXUNJIClockConfig)
                  000021e3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000e8     UNINITIALIZED
                  20200000    000000bc     (.common:gWHEELBackup)
                  202000bc    00000010     WTKJ.o (.bss.buff)
                  202000cc    0000000e     xunji.o (.bss.buffer)
                  202000da    00000002     --HOLE--
                  202000dc    0000000c     WTKJ.o (.bss.xyz)

.data      0    202000e8    0000001c     UNINITIALIZED
                  202000e8    00000010     wheel.o (.data.count_save.count)
                  202000f8    0000000a     xunji.o (.data.data)
                  20200102    00000001     WTKJ.o (.data.i)
                  20200103    00000001     xunji.o (.data.i)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1674   35        188    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        64     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1746   227       188    
                                                              
    .\BSP\
       wheel.o                        3394   0         16     
       OLED.o                         748    0         0      
       WTKJ.o                         496    0         29     
       xunji.o                        290    0         25     
       led.o                          76     0         0      
       delay.o                        22     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         5026   0         70     
                                                              
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         456    0         0      
                                                              
    D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       aeabi_idivmod.S.obj            86     0         0      
       fixdfsi.S.obj                  74     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         894    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      44        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8418   271       770    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002204 records: 2, size/record: 8, table size: 16
	.bss: load addr=000021f4, load size=00000008 bytes, run addr=20200000, run size=000000e8 bytes, compression=zero_init
	.data: load addr=000021fc, load size=00000008 bytes, run addr=202000e8, run size=0000001c bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000021e8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000021ab  ADC0_IRQHandler                 
000021ab  ADC1_IRQHandler                 
000021ab  AES_IRQHandler                  
000021ae  C$$EXIT                         
000021ab  CANFD0_IRQHandler               
000021ab  DAC0_IRQHandler                 
00002173  DL_Common_delayCycles           
00000c19  DL_Timer_initFourCCPWMMode      
00001db5  DL_Timer_setCaptCompUpdateMethod
00001f65  DL_Timer_setCaptureCompareOutCtl
00002155  DL_Timer_setCaptureCompareValue 
00001dd1  DL_Timer_setClockConfig         
0000153d  DL_UART_init                    
0000210d  DL_UART_setClockConfig          
000021ab  DMA_IRQHandler                  
000021ab  Default_Handler                 
000021ab  GROUP0_IRQHandler               
000021ab  GROUP1_IRQHandler               
000021af  HOSTexit                        
000021ab  HardFault_Handler               
000021ab  I2C0_IRQHandler                 
000021ab  I2C1_IRQHandler                 
00001d05  LED_OFF                         
000021ab  NMI_Handler                     
000012f9  OLED_Clear                      
00001fdb  OLED_I2C_Init                   
000013fd  OLED_I2C_SendByte               
00001cc1  OLED_I2C_Start                  
00001e09  OLED_I2C_Stop                   
00000fad  OLED_Init                       
00001a9d  OLED_SetCursor                  
00001bc9  OLED_WriteCommand               
00001bf3  OLED_WriteData                  
000021ab  PendSV_Handler                  
000021ab  RTC_IRQHandler                  
000021b3  Reset_Handler                   
000021ab  SPI0_IRQHandler                 
000021ab  SPI1_IRQHandler                 
000021ab  SVC_Handler                     
00000235  SYSCFG_DL_GPIO_init             
00001c1d  SYSCFG_DL_SYSCTL_init           
00000dfd  SYSCFG_DL_WHEEL_init            
00001585  SYSCFG_DL_WTKJ_init             
000015cd  SYSCFG_DL_XUNJI_init            
00001c71  SYSCFG_DL_init                  
00001291  SYSCFG_DL_initPower             
000021ab  SysTick_Handler                 
000021ab  TIMA0_IRQHandler                
000021ab  TIMA1_IRQHandler                
000021ab  TIMG0_IRQHandler                
000021ab  TIMG12_IRQHandler               
000021ab  TIMG6_IRQHandler                
000021ab  TIMG7_IRQHandler                
000021ab  TIMG8_IRQHandler                
0000211f  TI_memcpy_small                 
00001e25  UART0_IRQHandler                
00001e41  UART1_IRQHandler                
000021ab  UART2_IRQHandler                
000021ab  UART3_IRQHandler                
00001ff1  WTKJ_init                       
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002204  __TI_CINIT_Base                 
00002214  __TI_CINIT_Limit                
00002214  __TI_CINIT_Warm                 
000021e8  __TI_Handler_Table_Base         
000021f4  __TI_Handler_Table_Limit        
00001895  __TI_auto_init_nobinit_nopinit  
00001215  __TI_decompress_lzss            
00002131  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
0000201d  __TI_zero_init_nomemset         
00000edf  __addsf3                        
000014f1  __aeabi_d2iz                    
0000169d  __aeabi_f2d                     
000019f5  __aeabi_f2iz                    
00000edf  __aeabi_fadd                    
00001191  __aeabi_fdiv                    
0000107d  __aeabi_fmul                    
00000ed5  __aeabi_fsub                    
00001859  __aeabi_i2f                     
00001351  __aeabi_idiv                    
00000c17  __aeabi_idiv0                   
00001351  __aeabi_idivmod                 
0000219d  __aeabi_memcpy                  
0000219d  __aeabi_memcpy4                 
0000219d  __aeabi_memcpy8                 
ffffffff  __binit__                       
00001191  __divsf3                        
0000169d  __extendsfdf2                   
000014f1  __fixdfsi                       
000019f5  __fixsfsi                       
00001859  __floatsisf                     
UNDEFED   __mpu_init                      
000018d1  __muldsi3                       
0000107d  __mulsf3                        
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000ed5  __subsf3                        
00001c99  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000021b7  _system_pre_init                
000021a5  abort                           
ffffffff  binit                           
000013a7  calculate_angle_diff            
00001109  count_save                      
00002007  delay_ms                        
20200000  gWHEELBackup                    
00002165  get_angle_value                 
00000000  interruptVectors                
000016dd  main                            
00001615  set_all_pwm_channels            
00001451  set_all_pwm_channels_separate   
000008a1  tb6612_set_direction            
0000217d  wheel_begin_go                  
00001659  wheel_get_count                 
00000389  wheel_go_back                   
0000171d  wheel_go_back_count_close       
00001a2d  wheel_go_back_openloop          
00000619  wheel_go_forward                
0000175d  wheel_go_forward_count_close    
00001a65  wheel_go_forward_openloop       
000004d1  wheel_go_left                   
0000179d  wheel_go_left_count_close       
0000190b  wheel_go_left_openloop          
0000075d  wheel_go_right                  
000017dd  wheel_go_right_count_close      
00001945  wheel_go_right_openloop         
00002187  wheel_init                      
00000af9  wheel_polarity_test             
000000c1  wheel_rotate                    
0000197f  wheel_rotate_left_openloop      
000019b9  wheel_rotate_right_openloop     
00002191  wheel_stop                      
00001fad  wtjk_getxyz                     


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  wheel_rotate                    
00000200  __STACK_SIZE                    
00000235  SYSCFG_DL_GPIO_init             
00000389  wheel_go_back                   
000004d1  wheel_go_left                   
00000619  wheel_go_forward                
0000075d  wheel_go_right                  
000008a1  tb6612_set_direction            
00000af9  wheel_polarity_test             
00000c17  __aeabi_idiv0                   
00000c19  DL_Timer_initFourCCPWMMode      
00000dfd  SYSCFG_DL_WHEEL_init            
00000ed5  __aeabi_fsub                    
00000ed5  __subsf3                        
00000edf  __addsf3                        
00000edf  __aeabi_fadd                    
00000fad  OLED_Init                       
0000107d  __aeabi_fmul                    
0000107d  __mulsf3                        
00001109  count_save                      
00001191  __aeabi_fdiv                    
00001191  __divsf3                        
00001215  __TI_decompress_lzss            
00001291  SYSCFG_DL_initPower             
000012f9  OLED_Clear                      
00001351  __aeabi_idiv                    
00001351  __aeabi_idivmod                 
000013a7  calculate_angle_diff            
000013fd  OLED_I2C_SendByte               
00001451  set_all_pwm_channels_separate   
000014f1  __aeabi_d2iz                    
000014f1  __fixdfsi                       
0000153d  DL_UART_init                    
00001585  SYSCFG_DL_WTKJ_init             
000015cd  SYSCFG_DL_XUNJI_init            
00001615  set_all_pwm_channels            
00001659  wheel_get_count                 
0000169d  __aeabi_f2d                     
0000169d  __extendsfdf2                   
000016dd  main                            
0000171d  wheel_go_back_count_close       
0000175d  wheel_go_forward_count_close    
0000179d  wheel_go_left_count_close       
000017dd  wheel_go_right_count_close      
00001859  __aeabi_i2f                     
00001859  __floatsisf                     
00001895  __TI_auto_init_nobinit_nopinit  
000018d1  __muldsi3                       
0000190b  wheel_go_left_openloop          
00001945  wheel_go_right_openloop         
0000197f  wheel_rotate_left_openloop      
000019b9  wheel_rotate_right_openloop     
000019f5  __aeabi_f2iz                    
000019f5  __fixsfsi                       
00001a2d  wheel_go_back_openloop          
00001a65  wheel_go_forward_openloop       
00001a9d  OLED_SetCursor                  
00001bc9  OLED_WriteCommand               
00001bf3  OLED_WriteData                  
00001c1d  SYSCFG_DL_SYSCTL_init           
00001c71  SYSCFG_DL_init                  
00001c99  _c_int00_noargs                 
00001cc1  OLED_I2C_Start                  
00001d05  LED_OFF                         
00001db5  DL_Timer_setCaptCompUpdateMethod
00001dd1  DL_Timer_setClockConfig         
00001e09  OLED_I2C_Stop                   
00001e25  UART0_IRQHandler                
00001e41  UART1_IRQHandler                
00001f65  DL_Timer_setCaptureCompareOutCtl
00001fad  wtjk_getxyz                     
00001fdb  OLED_I2C_Init                   
00001ff1  WTKJ_init                       
00002007  delay_ms                        
0000201d  __TI_zero_init_nomemset         
0000210d  DL_UART_setClockConfig          
0000211f  TI_memcpy_small                 
00002131  __TI_decompress_none            
00002155  DL_Timer_setCaptureCompareValue 
00002165  get_angle_value                 
00002173  DL_Common_delayCycles           
0000217d  wheel_begin_go                  
00002187  wheel_init                      
00002191  wheel_stop                      
0000219d  __aeabi_memcpy                  
0000219d  __aeabi_memcpy4                 
0000219d  __aeabi_memcpy8                 
000021a5  abort                           
000021ab  ADC0_IRQHandler                 
000021ab  ADC1_IRQHandler                 
000021ab  AES_IRQHandler                  
000021ab  CANFD0_IRQHandler               
000021ab  DAC0_IRQHandler                 
000021ab  DMA_IRQHandler                  
000021ab  Default_Handler                 
000021ab  GROUP0_IRQHandler               
000021ab  GROUP1_IRQHandler               
000021ab  HardFault_Handler               
000021ab  I2C0_IRQHandler                 
000021ab  I2C1_IRQHandler                 
000021ab  NMI_Handler                     
000021ab  PendSV_Handler                  
000021ab  RTC_IRQHandler                  
000021ab  SPI0_IRQHandler                 
000021ab  SPI1_IRQHandler                 
000021ab  SVC_Handler                     
000021ab  SysTick_Handler                 
000021ab  TIMA0_IRQHandler                
000021ab  TIMA1_IRQHandler                
000021ab  TIMG0_IRQHandler                
000021ab  TIMG12_IRQHandler               
000021ab  TIMG6_IRQHandler                
000021ab  TIMG7_IRQHandler                
000021ab  TIMG8_IRQHandler                
000021ab  UART2_IRQHandler                
000021ab  UART3_IRQHandler                
000021ae  C$$EXIT                         
000021af  HOSTexit                        
000021b3  Reset_Handler                   
000021b7  _system_pre_init                
000021e8  __TI_Handler_Table_Base         
000021f4  __TI_Handler_Table_Limit        
00002204  __TI_CINIT_Base                 
00002214  __TI_CINIT_Limit                
00002214  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gWHEELBackup                    
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[150 symbols]
