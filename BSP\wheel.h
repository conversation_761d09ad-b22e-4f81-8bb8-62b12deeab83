

#ifndef WHEEL_H
#define WHEEL_H

/**
 * @file wheel.h
 * @brief 锟斤拷锟接匡拷锟斤拷模锟斤拷头锟侥硷拷
 */

/* 系统头锟侥硷拷锟斤拷锟斤拷 */
#include "ti_msp_dl_config.h"

/* 锟斤拷锟狡诧拷锟斤拷锟斤拷睾甓拷锟� */
// 锟劫度匡拷锟狡诧拷锟斤拷
#define MIN_ROTATION_DUTY_CYCLE 100
#define ROTATION_SPEED 300

// PD锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
#define BACK_KP 10
#define BACK_KD 0
#define STRAIGHT_KP 5 // 直锟斤拷PD锟斤拷锟斤拷
#define STRAIGHT_KD 0
#define LEFT_KP 30 // 锟斤拷锟斤拷锟斤拷转PD锟斤拷锟斤拷
#define LEFT_KD 0
#define RIGHT_KP 30 // 锟斤拷锟斤拷锟斤拷转PD锟斤拷锟斤拷
#define RIGHT_KD 0
#define ROTATE_KP 5 // 锟斤拷转PD锟斤拷锟斤拷
#define ROTATE_KD 0.2

// 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
#define GYRO_POLARITY 1 // 锟斤拷锟斤拷锟角角度硷拷锟皆ｏ拷0锟斤拷示锟角度硷拷锟斤拷1锟斤拷示锟角度硷拷

/* 锟斤拷锟酵讹拷锟斤拷 */
typedef struct
{
    float kp;         // 锟斤拷锟斤拷系锟斤拷
    float kd;         // 微锟斤拷系锟斤拷
    float prev_error; // 锟斤拷一锟斤拷锟斤拷锟�
} PDController;

/* 锟斤拷锟斤拷锟斤拷锟斤拷 */
// 锟斤拷始锟斤拷锟斤拷锟斤拷
void wheel_init(void);
void wheel_begin_go(void);

// 锟斤拷锟斤拷锟斤拷锟狡猴拷锟斤拷
void wheel_stop(void);
void set_all_pwm_channels(uint16_t speed);
void set_all_pwm_channels_separate(uint16_t speed1, uint16_t speed2, uint16_t speed3, uint16_t speed4);
void tb6612_set_direction(uint8_t motor_num, uint8_t polarity);

// 锟秸伙拷锟斤拷锟狡猴拷锟斤拷
void wheel_go_back(uint16_t now_z, uint16_t speed);
// 锟睫改猴拷锟斤拷锟斤拷
void wheel_go_forward(uint16_t now_z, uint16_t speed);
void wheel_go_right(uint16_t now_z, uint16_t speed);
void wheel_go_left(uint16_t now_z, uint16_t speed);
void wheel_rotate(int16_t angle);

// 锟斤拷锟斤拷锟斤拷锟狡猴拷锟斤拷
void wheel_go_forward_openloop(uint16_t speed);
void wheel_go_back_openloop(uint16_t speed);
void wheel_go_left_openloop(uint16_t speed);
void wheel_go_right_openloop(uint16_t speed);
void wheel_rotate_left_openloop(uint16_t speed);
void wheel_rotate_right_openloop(uint16_t speed);

// 锟斤拷锟斤拷锟斤拷锟斤拷锟侥闭伙拷锟斤拷锟狡猴拷锟斤拷
// 锟睫改猴拷锟斤拷锟斤拷
void wheel_go_forward_count_close(uint16_t count, uint16_t speed);
void wheel_go_back_count_close(uint16_t count, uint16_t speed);
void wheel_go_left_count_close(uint16_t count, uint16_t speed);
void wheel_go_right_count_close(uint16_t count, uint16_t speed);

// 锟斤拷锟斤拷锟斤拷锟斤拷锟侥匡拷锟斤拷锟斤拷锟狡猴拷锟斤拷
void wheel_go_forward_count_open(uint16_t count, uint16_t speed);
void wheel_go_back_count_open(uint16_t count, uint16_t speed);
void wheel_go_left_count_open(uint16_t count, uint16_t speed);
void wheel_go_right_count_open(uint16_t count, uint16_t speed);

// 锟斤拷锟斤拷锟斤拷锟斤拷
uint16_t clamp_speed(uint16_t speed, uint16_t error);
uint16_t get_angle_value(void);
uint16_t wheel_get_count(void);
int32_t count_save(uint8_t cmd);
void wheel_polarity_test(void);

#endif // WHEEL_H

/*
1                4
2                3
锟斤拷锟矫好讹拷应锟斤拷藕锟绞癸拷锟�
wheel_polarity_test(void);
锟斤拷锟斤拷锟斤拷锟饺凤拷锟斤拷锟斤拷锟斤拷欠锟斤拷锟饺凤拷锟斤拷欠锟斤拷锟�
1234锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟阶�
然锟斤拷
锟斤拷转
锟斤拷转
锟斤拷锟斤拷转
锟斤拷锟斤拷转
锟斤拷锟斤拷
前锟斤拷
锟侥凤拷式锟剿讹拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷陆锟斤拷锟�
*/