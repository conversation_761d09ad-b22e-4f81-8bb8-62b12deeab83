/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for WHEEL */
#define WHEEL_INST                                                         TIMA0
#define WHEEL_INST_IRQHandler                                   TIMA0_IRQHandler
#define WHEEL_INST_INT_IRQN                                     (TIMA0_INT_IRQn)
#define WHEEL_INST_CLK_FREQ                                             32000000
/* GPIO defines for channel 0 */
#define GPIO_WHEEL_C0_PORT                                                 GPIOA
#define GPIO_WHEEL_C0_PIN                                         DL_GPIO_PIN_21
#define GPIO_WHEEL_C0_IOMUX                                      (IOMUX_PINCM46)
#define GPIO_WHEEL_C0_IOMUX_FUNC                     IOMUX_PINCM46_PF_TIMA0_CCP0
#define GPIO_WHEEL_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_WHEEL_C1_PORT                                                 GPIOA
#define GPIO_WHEEL_C1_PIN                                         DL_GPIO_PIN_22
#define GPIO_WHEEL_C1_IOMUX                                      (IOMUX_PINCM47)
#define GPIO_WHEEL_C1_IOMUX_FUNC                     IOMUX_PINCM47_PF_TIMA0_CCP1
#define GPIO_WHEEL_C1_IDX                                    DL_TIMER_CC_1_INDEX
/* GPIO defines for channel 2 */
#define GPIO_WHEEL_C2_PORT                                                 GPIOA
#define GPIO_WHEEL_C2_PIN                                         DL_GPIO_PIN_10
#define GPIO_WHEEL_C2_IOMUX                                      (IOMUX_PINCM21)
#define GPIO_WHEEL_C2_IOMUX_FUNC                     IOMUX_PINCM21_PF_TIMA0_CCP2
#define GPIO_WHEEL_C2_IDX                                    DL_TIMER_CC_2_INDEX
/* GPIO defines for channel 3 */
#define GPIO_WHEEL_C3_PORT                                                 GPIOA
#define GPIO_WHEEL_C3_PIN                                         DL_GPIO_PIN_12
#define GPIO_WHEEL_C3_IOMUX                                      (IOMUX_PINCM34)
#define GPIO_WHEEL_C3_IOMUX_FUNC                     IOMUX_PINCM34_PF_TIMA0_CCP3
#define GPIO_WHEEL_C3_IDX                                    DL_TIMER_CC_3_INDEX



/* Defines for WTKJ */
#define WTKJ_INST                                                          UART1
#define WTKJ_INST_FREQUENCY                                             32000000
#define WTKJ_INST_IRQHandler                                    UART1_IRQHandler
#define WTKJ_INST_INT_IRQN                                        UART1_INT_IRQn
#define GPIO_WTKJ_RX_PORT                                                  GPIOA
#define GPIO_WTKJ_TX_PORT                                                  GPIOA
#define GPIO_WTKJ_RX_PIN                                          DL_GPIO_PIN_18
#define GPIO_WTKJ_TX_PIN                                          DL_GPIO_PIN_17
#define GPIO_WTKJ_IOMUX_RX                                       (IOMUX_PINCM40)
#define GPIO_WTKJ_IOMUX_TX                                       (IOMUX_PINCM39)
#define GPIO_WTKJ_IOMUX_RX_FUNC                        IOMUX_PINCM40_PF_UART1_RX
#define GPIO_WTKJ_IOMUX_TX_FUNC                        IOMUX_PINCM39_PF_UART1_TX
#define WTKJ_BAUD_RATE                                                  (115200)
#define WTKJ_IBRD_32_MHZ_115200_BAUD                                        (17)
#define WTKJ_FBRD_32_MHZ_115200_BAUD                                        (23)
/* Defines for XUNJI */
#define XUNJI_INST                                                         UART0
#define XUNJI_INST_FREQUENCY                                            32000000
#define XUNJI_INST_IRQHandler                                   UART0_IRQHandler
#define XUNJI_INST_INT_IRQN                                       UART0_INT_IRQn
#define GPIO_XUNJI_RX_PORT                                                 GPIOA
#define GPIO_XUNJI_TX_PORT                                                 GPIOA
#define GPIO_XUNJI_RX_PIN                                         DL_GPIO_PIN_11
#define GPIO_XUNJI_TX_PIN                                         DL_GPIO_PIN_28
#define GPIO_XUNJI_IOMUX_RX                                      (IOMUX_PINCM22)
#define GPIO_XUNJI_IOMUX_TX                                       (IOMUX_PINCM3)
#define GPIO_XUNJI_IOMUX_RX_FUNC                       IOMUX_PINCM22_PF_UART0_RX
#define GPIO_XUNJI_IOMUX_TX_FUNC                        IOMUX_PINCM3_PF_UART0_TX
#define XUNJI_BAUD_RATE                                                 (115200)
#define XUNJI_IBRD_32_MHZ_115200_BAUD                                       (17)
#define XUNJI_FBRD_32_MHZ_115200_BAUD                                       (23)





/* Port definition for Pin Group OLED */
#define OLED_PORT                                                        (GPIOB)

/* Defines for SCL: GPIOB.24 with pinCMx 52 on package pin 23 */
#define OLED_SCL_PIN                                            (DL_GPIO_PIN_24)
#define OLED_SCL_IOMUX                                           (IOMUX_PINCM52)
/* Defines for SDA: GPIOB.18 with pinCMx 44 on package pin 15 */
#define OLED_SDA_PIN                                            (DL_GPIO_PIN_18)
#define OLED_SDA_IOMUX                                           (IOMUX_PINCM44)
/* Port definition for Pin Group LEBEEP */
#define LEBEEP_PORT                                                      (GPIOA)

/* Defines for LED: GPIOA.2 with pinCMx 7 on package pin 42 */
#define LEBEEP_LED_PIN                                           (DL_GPIO_PIN_2)
#define LEBEEP_LED_IOMUX                                          (IOMUX_PINCM7)
/* Defines for BEEP: GPIOA.30 with pinCMx 5 on package pin 37 */
#define LEBEEP_BEEP_PIN                                         (DL_GPIO_PIN_30)
#define LEBEEP_BEEP_IOMUX                                         (IOMUX_PINCM5)
/* Defines for KEY1: GPIOB.21 with pinCMx 49 on package pin 20 */
#define KEY_KEY1_PORT                                                    (GPIOB)
#define KEY_KEY1_PIN                                            (DL_GPIO_PIN_21)
#define KEY_KEY1_IOMUX                                           (IOMUX_PINCM49)
/* Defines for KEY2: GPIOA.7 with pinCMx 14 on package pin 49 */
#define KEY_KEY2_PORT                                                    (GPIOA)
#define KEY_KEY2_PIN                                             (DL_GPIO_PIN_7)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM14)
/* Defines for KEY3: GPIOB.14 with pinCMx 31 on package pin 2 */
#define KEY_KEY3_PORT                                                    (GPIOB)
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_14)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM31)
/* Defines for KEY4: GPIOB.17 with pinCMx 43 on package pin 14 */
#define KEY_KEY4_PORT                                                    (GPIOB)
#define KEY_KEY4_PIN                                            (DL_GPIO_PIN_17)
#define KEY_KEY4_IOMUX                                           (IOMUX_PINCM43)
/* Defines for PWM11: GPIOA.8 with pinCMx 19 on package pin 54 */
#define Direction_PWM11_PORT                                             (GPIOA)
#define Direction_PWM11_PIN                                      (DL_GPIO_PIN_8)
#define Direction_PWM11_IOMUX                                    (IOMUX_PINCM19)
/* Defines for PWM12: GPIOA.9 with pinCMx 20 on package pin 55 */
#define Direction_PWM12_PORT                                             (GPIOA)
#define Direction_PWM12_PIN                                      (DL_GPIO_PIN_9)
#define Direction_PWM12_IOMUX                                    (IOMUX_PINCM20)
/* Defines for PWM21: GPIOB.15 with pinCMx 32 on package pin 3 */
#define Direction_PWM21_PORT                                             (GPIOB)
#define Direction_PWM21_PIN                                     (DL_GPIO_PIN_15)
#define Direction_PWM21_IOMUX                                    (IOMUX_PINCM32)
/* Defines for PWM22: GPIOB.16 with pinCMx 33 on package pin 4 */
#define Direction_PWM22_PORT                                             (GPIOB)
#define Direction_PWM22_PIN                                     (DL_GPIO_PIN_16)
#define Direction_PWM22_IOMUX                                    (IOMUX_PINCM33)
/* Defines for PWM31: GPIOB.2 with pinCMx 15 on package pin 50 */
#define Direction_PWM31_PORT                                             (GPIOB)
#define Direction_PWM31_PIN                                      (DL_GPIO_PIN_2)
#define Direction_PWM31_IOMUX                                    (IOMUX_PINCM15)
/* Defines for PWM32: GPIOB.3 with pinCMx 16 on package pin 51 */
#define Direction_PWM32_PORT                                             (GPIOB)
#define Direction_PWM32_PIN                                      (DL_GPIO_PIN_3)
#define Direction_PWM32_IOMUX                                    (IOMUX_PINCM16)
/* Defines for PWM41: GPIOB.6 with pinCMx 23 on package pin 58 */
#define Direction_PWM41_PORT                                             (GPIOB)
#define Direction_PWM41_PIN                                      (DL_GPIO_PIN_6)
#define Direction_PWM41_IOMUX                                    (IOMUX_PINCM23)
/* Defines for PWM42: GPIOB.8 with pinCMx 25 on package pin 60 */
#define Direction_PWM42_PORT                                             (GPIOB)
#define Direction_PWM42_PIN                                      (DL_GPIO_PIN_8)
#define Direction_PWM42_IOMUX                                    (IOMUX_PINCM25)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_WHEEL_init(void);
void SYSCFG_DL_WTKJ_init(void);
void SYSCFG_DL_XUNJI_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
