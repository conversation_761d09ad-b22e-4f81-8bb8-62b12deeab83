/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();
const UART2  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux6       = system.clockTree["FCCSELCLKMUX"];
mux6.inputSelect = "FCCSELCLKMUX_EXTCLK";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.enable    = true;
pinFunction4.inputFreq = 40;

GPIO1.$name                          = "OLED";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name        = "SCL";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "24";
GPIO1.associatedPins[1].$name        = "SDA";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "18";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                          = "LEBEEP";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name        = "LED";
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[0].assignedPort = "PORTA";
GPIO2.associatedPins[0].assignedPin  = "2";
GPIO2.associatedPins[1].$name        = "BEEP";
GPIO2.associatedPins[1].initialValue = "SET";
GPIO2.associatedPins[1].assignedPort = "PORTA";
GPIO2.associatedPins[1].assignedPin  = "30";

GPIO3.$name                              = "KEY";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name            = "KEY1";
GPIO3.associatedPins[0].initialValue     = "SET";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].assignedPort     = "PORTB";
GPIO3.associatedPins[0].assignedPin      = "21";
GPIO3.associatedPins[0].internalResistor = "PULL_UP";
GPIO3.associatedPins[1].$name            = "KEY2";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].assignedPort     = "PORTA";
GPIO3.associatedPins[1].assignedPin      = "7";
GPIO3.associatedPins[1].internalResistor = "PULL_UP";
GPIO3.associatedPins[2].$name            = "KEY3";
GPIO3.associatedPins[2].direction        = "INPUT";
GPIO3.associatedPins[2].assignedPort     = "PORTB";
GPIO3.associatedPins[2].assignedPin      = "14";
GPIO3.associatedPins[2].internalResistor = "PULL_UP";
GPIO3.associatedPins[3].$name            = "KEY4";
GPIO3.associatedPins[3].direction        = "INPUT";
GPIO3.associatedPins[3].assignedPin      = "17";
GPIO3.associatedPins[3].assignedPort     = "PORTB";
GPIO3.associatedPins[3].internalResistor = "PULL_UP";

GPIO4.$name                          = "Direction";
GPIO4.associatedPins.create(8);
GPIO4.associatedPins[0].$name        = "PWM11";
GPIO4.associatedPins[0].assignedPort = "PORTA";
GPIO4.associatedPins[0].assignedPin  = "8";
GPIO4.associatedPins[1].$name        = "PWM12";
GPIO4.associatedPins[1].assignedPort = "PORTA";
GPIO4.associatedPins[1].assignedPin  = "9";
GPIO4.associatedPins[1].initialValue = "SET";
GPIO4.associatedPins[2].$name        = "PWM21";
GPIO4.associatedPins[2].assignedPort = "PORTB";
GPIO4.associatedPins[2].assignedPin  = "15";
GPIO4.associatedPins[3].$name        = "PWM22";
GPIO4.associatedPins[3].assignedPort = "PORTB";
GPIO4.associatedPins[3].assignedPin  = "16";
GPIO4.associatedPins[3].initialValue = "SET";
GPIO4.associatedPins[4].$name        = "PWM31";
GPIO4.associatedPins[4].assignedPort = "PORTB";
GPIO4.associatedPins[4].assignedPin  = "2";
GPIO4.associatedPins[4].initialValue = "SET";
GPIO4.associatedPins[5].$name        = "PWM32";
GPIO4.associatedPins[5].assignedPort = "PORTB";
GPIO4.associatedPins[5].assignedPin  = "3";
GPIO4.associatedPins[6].$name        = "PWM41";
GPIO4.associatedPins[6].assignedPort = "PORTB";
GPIO4.associatedPins[6].assignedPin  = "6";
GPIO4.associatedPins[7].$name        = "PWM42";
GPIO4.associatedPins[7].assignedPin  = "8";
GPIO4.associatedPins[7].assignedPort = "PORTB";

PWM1.$name                              = "WHEEL";
PWM1.ccIndex                            = [0,1,2,3];
PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM1.peripheral.$assign                 = "TIMA0";
PWM1.peripheral.ccp0Pin.$assign         = "PA21";
PWM1.peripheral.ccp1Pin.$assign         = "PA22";
PWM1.peripheral.ccp2Pin.$assign         = "PA10";
PWM1.peripheral.ccp3Pin.$assign         = "PA12";
PWM1.PWM_CHANNEL_2.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.PWM_CHANNEL_3.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM1.ccp2PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM1.ccp3PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

SYSCTL.forceDefaultClkConfig = true;

UART1.$name                            = "WTKJ";
UART1.targetBaudRate                   = 115200;
UART1.enabledInterrupts                = ["RX"];
UART1.peripheral.$assign               = "UART1";
UART1.peripheral.rxPin.$assign         = "PA18";
UART1.peripheral.txPin.$assign         = "PA17";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                            = "XUNJI";
UART2.enabledInterrupts                = ["RX"];
UART2.targetBaudRate                   = 115200;
UART2.peripheral.rxPin.$assign         = "PA11";
UART2.peripheral.txPin.$assign         = "PA28";
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB24";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PB18";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PA2";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PA30";
GPIO3.associatedPins[0].pin.$suggestSolution       = "PB21";
GPIO3.associatedPins[1].pin.$suggestSolution       = "PA7";
GPIO3.associatedPins[2].pin.$suggestSolution       = "PB14";
GPIO3.associatedPins[3].pin.$suggestSolution       = "PB17";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PA8";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PA9";
GPIO4.associatedPins[2].pin.$suggestSolution       = "PB15";
GPIO4.associatedPins[3].pin.$suggestSolution       = "PB16";
GPIO4.associatedPins[4].pin.$suggestSolution       = "PB2";
GPIO4.associatedPins[5].pin.$suggestSolution       = "PB3";
GPIO4.associatedPins[6].pin.$suggestSolution       = "PB6";
GPIO4.associatedPins[7].pin.$suggestSolution       = "PB8";
SYSCTL.peripheral.$suggestSolution                 = "SYSCTL";
UART2.peripheral.$suggestSolution                  = "UART0";
