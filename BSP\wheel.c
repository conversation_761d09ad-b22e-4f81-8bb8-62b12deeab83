#include "wheel.h"
#include "WTKJ.h"
#include "delay.h"

// 静态变量，用于存储偏航角
static float yaw = 0;

/* ==================== 分隔线 ==================== */

/**
 * @brief 限制速度值，确保其不会小于 error 值
 * @param speed 输入的速度值
 * @param error 误差值，用于限制速度
 * @return 经过限制后的速度值
 */
uint16_t clamp_speed(uint16_t speed, uint16_t error)
{
    // 如果输入速度大于误差值
    if (speed > error)
    {
        // 返回速度减去误差的值
        return speed - error;
    }
    // 否则返回 0
    return 0;
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?8?0?7?1?7С?1?7?1?70
 * @param speed ?1?7?1?7?0?2?1?7?1?8?1?7?0?5
 * @param error ?1?7?1?7?1?7?0?5
 * @return ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?8?1?7?0?5
 */
uint16_t limit_speed(uint16_t speed, uint16_t max_speed)
{
    if (speed > max_speed)
    {
        return max_speed;
    }
    return speed;
}

/* ==================== ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?6?1?7?1?7?1?7 ==================== */

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?0?0?1?7?0?0?1?7?1?7PWM?0?8?1?7?0?7?1?7(?1?7?1?7?0?4?0?5)
 * @param speed PWM?0?8?1?7?0?7?1?7?0?5
 */
void set_all_pwm_channels(uint16_t speed)
{
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed, GPIO_WHEEL_C0_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed, GPIO_WHEEL_C1_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed, GPIO_WHEEL_C2_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed, GPIO_WHEEL_C3_IDX);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?0?0?1?7?0?0?1?7?1?7PWM?0?8?1?7?0?7?1?7(?1?7?1?7?0?4?0?5)
 * @param speed1 ?0?0?1?7?1?71PWM?0?5
 * @param speed2 ?0?0?1?7?1?72PWM?0?5
 * @param speed3 ?0?0?1?7?1?73PWM?0?5
 * @param speed4 ?0?0?1?7?1?74PWM?0?5
 */
void set_all_pwm_channels_separate(uint16_t speed1, uint16_t speed2, uint16_t speed3, uint16_t speed4)
{
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed1, GPIO_WHEEL_C0_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed3, GPIO_WHEEL_C1_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed4, GPIO_WHEEL_C2_IDX);
    DL_TimerG_setCaptureCompareValue(WHEEL_INST, speed2, GPIO_WHEEL_C3_IDX);
}

/**
 * @brief ?1?7?1?7?1?7?1?7TB6612?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param motor_num ?1?7?1?7?1?7?1?7?1?7?1?7(1-4)
 * @param polarity ?1?7?1?7?1?7?1?7(0?1?7?1?71)
 */
void tb6612_set_direction(uint8_t motor_num, uint8_t polarity)
{
    switch (motor_num)
    {
    case 1:
        if (polarity == 0)
        {
            DL_GPIO_setPins(Direction_PWM11_PORT, Direction_PWM11_PIN);
            DL_GPIO_clearPins(Direction_PWM12_PORT, Direction_PWM12_PIN);
        }
        else
        {
            DL_GPIO_clearPins(Direction_PWM11_PORT, Direction_PWM11_PIN);
            DL_GPIO_setPins(Direction_PWM12_PORT, Direction_PWM12_PIN);
        }
        break;
    case 3:
        if (polarity == 0)
        {
            DL_GPIO_clearPins(Direction_PWM21_PORT, Direction_PWM21_PIN);
            DL_GPIO_setPins(Direction_PWM22_PORT, Direction_PWM22_PIN);
        }
        else
        {

            DL_GPIO_setPins(Direction_PWM21_PORT, Direction_PWM21_PIN);
            DL_GPIO_clearPins(Direction_PWM22_PORT, Direction_PWM22_PIN);
        }
        break;
    case 4:
        if (polarity == 0)
        {
            DL_GPIO_setPins(Direction_PWM31_PORT, Direction_PWM31_PIN);
            DL_GPIO_clearPins(Direction_PWM32_PORT, Direction_PWM32_PIN);
        }
        else
        {
            DL_GPIO_clearPins(Direction_PWM31_PORT, Direction_PWM31_PIN);
            DL_GPIO_setPins(Direction_PWM32_PORT, Direction_PWM32_PIN);
        }
        break;
    case 2:
        if (polarity == 0)
        {
            DL_GPIO_clearPins(Direction_PWM41_PORT, Direction_PWM41_PIN);
            DL_GPIO_setPins(Direction_PWM42_PORT, Direction_PWM42_PIN);
        }
        else
        {
            DL_GPIO_setPins(Direction_PWM41_PORT, Direction_PWM41_PIN);
            DL_GPIO_clearPins(Direction_PWM42_PORT, Direction_PWM42_PIN);
        }
        break;
    default:
        break;
    }
}

/**
 * @brief ?1?7?1?7?0?3?1?7?1?7?1?7?1?7?1?7?0?3?1?7?1?7?1?7?0?0?1?7?1?7
 */
void wheel_init()
{
    set_all_pwm_channels(0);
}

/**
 * @brief ?0?5?0?9?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 */
void wheel_stop()
{
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?0?3?1?7?0?6?1?7?1?7?1?7?1?7?1?7?1?7?0?9?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 */
void wheel_begin_go()
{
    count_save(8);
}

/**
 * @brief ?1?7?1?7?0?0?1?7?1?7?1?7?1?7?1?7?1?7?0?9?1?7?1?7?1?7?1?7?1?7?1?7
 * @return ?1?7?0?0?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?9?1?7?1?7?1?7?1?7?1?7?1?7?0?5
 */
uint16_t wheel_get_count()
{
    return (count_save(4) + count_save(5) + count_save(6) + count_save(7)) / 4;
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?0?1?7?1?7?1?7?1?7
 * @param cmd ?1?7?1?7?1?7?1?7?1?7?0?30-3 ?0?2?1?7?1?7?1?7?1?7?1?7?1?7 1?1?7?1?74-7 ?0?2?1?7?1?7?0?0?1?7?1?7?1?7?1?7?1?7?1?78 ?0?2?1?7?1?7?1?7ü?1?7?1?7?1?7

 * @return ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?8?1?7?1?7?1?7?1?7?0?5?1?7?1?7?1?7?1?9?1?7?1?7?1?7?0?5?1?7?1?7?1?7?7?4?1?7?1?7 0
 */
int32_t count_save(uint8_t cmd)
{
    static int32_t count[4] = {0};
    if (cmd < 4)
    {
        count[cmd]++;
    }
    else if (cmd < 8)
    {
        return count[cmd - 4];
    }
    else if (cmd == 8)
    {
        for (uint8_t i = 0; i < 4; i++)
            count[i] = 0;
    }
    return 0;
}

/**
 * @brief GPIO ?1?7?8?9?1?7ж?0?9?1?3?1?7?1?7?1?7?1?7?1?7
 * @param GPIO_Pin ?1?7?1?7?1?7?1?7?1?7ж?0?3?1?7 GPIO ?1?7?1?7?1?7?1?7
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    /*switch (GPIO_Pin)
     {
     case ENCODER_CHA_1_PIN:
         count_save(0);
         break;
     case ENCODER_CHA_2_PIN:
         count_save(1);
         break;
     case ENCODER_CHA_3_PIN:
         count_save(2);
         break;
     case ENCODER_CHA_4_PIN:
         count_save(3);
         break;
     default:
         break;
     }
     */
}

/**
 * @brief ?1?7?1?7?0?0?1?7?1?7?0?2?1?7?0?8?1?7?0?5
 * @return ?1?7?1?7?0?2?1?7?0?8?1?7?0?5?1?7?1?7?1?7?1?7?1?7?1?7 get_xyz_z ?1?7?1?7?1?7?1?7?1?7?1?7?0?0
 */
uint16_t get_angle_value()
{
    return (uint16_t)wtjk_getxyz(2);
}

/* ==================== ?1?7?0?7?1?7?1?7?1?7?1?7?0?6?1?7?1?7?1?7 ==================== */

/**





















 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7(?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7)
 * @param now_z ?1?7?1?7?0?2?1?7?0?8?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_back(uint16_t now_z, uint16_t speed)
{
    PDController pd = {BACK_KP, BACK_KD, 0};
    tb6612_set_direction(1, 1);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 1);

    uint16_t z = get_angle_value();
    int16_t diff = calculate_angle_diff(now_z, z);

    float derivative = diff - pd.prev_error;
    float output = pd.kp * diff + pd.kd * derivative;
    pd.prev_error = diff;

    uint16_t adjustment = (uint16_t)fabs(output);
    adjustment = (adjustment > speed) ? speed : adjustment;

    if (diff == 0)
    {
        set_all_pwm_channels(speed);
    }
    else if (diff > 0)
    {
        uint16_t left_speed = speed - adjustment;
        uint16_t right_speed = speed;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
    else
    {
        uint16_t left_speed = speed;
        uint16_t right_speed = speed - adjustment;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
}

/**
 * @brief ?0?1?1?7п?1?7?1?7?1?7(?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7)
 * @param now_z ?1?7?1?7?0?2?1?7?0?8?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_forward(uint16_t now_z, uint16_t speed)
{
    PDController pd = {STRAIGHT_KP, STRAIGHT_KD, 0};
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 0);
    tb6612_set_direction(3, 0);
    tb6612_set_direction(4, 0);

    uint16_t z = get_angle_value();
    int16_t diff = calculate_angle_diff(now_z, z);

    float derivative = diff - pd.prev_error;
    float output = pd.kp * diff + pd.kd * derivative;
    pd.prev_error = diff;

    uint16_t adjustment = (uint16_t)fabs(output);
    adjustment = (adjustment > speed) ? speed : adjustment;

    if (diff == 0)
    {
        set_all_pwm_channels(speed);
    }
    else if (diff > 0)
    {
        uint16_t left_speed = speed;
        uint16_t right_speed = speed - adjustment;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
    else
    {
        uint16_t left_speed = speed - adjustment;
        uint16_t right_speed = speed;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
}

/**
 * @brief ?1?7?1?7?1?7?0?1?1?7?1?7?1?7(?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7)
 * @param now_z ?1?7?1?7?0?2?1?7?0?8?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_left(uint16_t now_z, uint16_t speed)
{
    PDController pd = {LEFT_KP, LEFT_KD, 0};
    tb6612_set_direction(1, 1);
    tb6612_set_direction(2, 0);
    tb6612_set_direction(3, 0);
    tb6612_set_direction(4, 1);

    uint16_t z = get_angle_value();
    int16_t diff = calculate_angle_diff(now_z, z);

    float derivative = diff - pd.prev_error;
    float output = pd.kp * diff + pd.kd * derivative;
    pd.prev_error = diff;

    uint16_t adjustment = (uint16_t)fabs(output);
    adjustment = (adjustment > speed) ? speed : adjustment;

    if (diff == 0)
    {
        set_all_pwm_channels(speed);
    }
    else if (diff > 0)
    {
        uint16_t left_speed = speed;
        uint16_t right_speed = speed - adjustment;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
    else
    {
        uint16_t left_speed = speed - adjustment;
        uint16_t right_speed = speed;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
}

/**
 * @brief 控制轮子向右移动（使用闭环控制）
 * @param now_z 当前的角度值
 * @param speed 目标移动速度
 */
void wheel_go_right(uint16_t now_z, uint16_t speed)
{
    // 初始化 PD 控制器，设置比例系数和微分系数
    PDController pd = {RIGHT_KP, RIGHT_KD, 0};
    // 设置 4 个电机的转动方向，实现向右移动
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 0);

    // 获取当前的角度值
    uint16_t z = get_angle_value();
    // 计算当前角度与目标角度的差值
    int16_t diff = calculate_angle_diff(now_z, z);

    // 计算微分项
    float derivative = diff - pd.prev_error;
    // 计算 PD 控制器的输出
    float output = pd.kp * diff + pd.kd * derivative;
    // 更新上一次的误差值
    pd.prev_error = diff;

    // 获取输出的绝对值，并转换为无符号 16 位整数
    uint16_t adjustment = (uint16_t)fabs(output);
    // 确保调整值不超过目标速度
    adjustment = (adjustment > speed) ? speed : adjustment;

    // 如果角度差值为 0，说明方向正确
    if (diff == 0)
    {
        // 所有电机以目标速度运行
        set_all_pwm_channels(speed);
    }
    // 如果角度差值大于 0，需要调整方向
    else if (diff > 0)
    {
        // 计算左侧电机的速度
        uint16_t left_speed = speed - adjustment;
        // 右侧电机保持目标速度
        uint16_t right_speed = speed;
        // 分别设置左右电机的速度
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
    // 如果角度差值小于 0，需要调整方向
    else
    {
        // 左侧电机保持目标速度
        uint16_t left_speed = speed;
        // 计算右侧电机的速度
        uint16_t right_speed = speed - adjustment;
        // 分别设置左右电机的速度
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
}

/**
 * @brief ?1?7?1?7?1?7?0?1?1?7?1?7?1?7(?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7)
 * @param now_z ?1?7?1?7?0?2?1?7?0?8?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_right(uint16_t now_z, uint16_t speed)
{
    PDController pd = {RIGHT_KP, RIGHT_KD, 0};
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 0);

    uint16_t z = get_angle_value();
    int16_t diff = calculate_angle_diff(now_z, z);

    float derivative = diff - pd.prev_error;
    float output = pd.kp * diff + pd.kd * derivative;
    pd.prev_error = diff;

    uint16_t adjustment = (uint16_t)fabs(output);
    adjustment = (adjustment > speed) ? speed : adjustment;

    if (diff == 0)
    {
        set_all_pwm_channels(speed);
    }
    else if (diff > 0)
    {
        uint16_t left_speed = speed - adjustment;
        uint16_t right_speed = speed;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
    else
    {
        uint16_t left_speed = speed;
        uint16_t right_speed = speed - adjustment;
        set_all_pwm_channels_separate(left_speed, left_speed, right_speed, right_speed);
    }
}

/**
 * @brief ?1?7?1?7?0?8?1?7?1?7?1?7?1?7(?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7)
 * @param angle ?0?7?1?7?1?7?1?7?1?7?0?8?1?7?0?8?1?7(?1?7?1?7?0?5?0?2?1?7?1?7?0?8?1?7?1?7?1?7?1?7?0?5?0?2?1?7?1?7?0?8)
 */
void wheel_rotate(int16_t angle)
{
    PDController pd = {ROTATE_KP, ROTATE_KD, 0};
    uint16_t initial_z = get_angle_value();
    uint16_t target_z = (uint16_t)((initial_z + angle + 360) % 360);

    if (angle < 0)
    {
        tb6612_set_direction(1, 0);
        tb6612_set_direction(2, 0);
        tb6612_set_direction(3, 1);
        tb6612_set_direction(4, 1);
    }
    else
    {
        tb6612_set_direction(1, 1);
        tb6612_set_direction(2, 1);
        tb6612_set_direction(3, 0);
        tb6612_set_direction(4, 0);
    }

    set_all_pwm_channels(0);

    while (1)
    {
        uint16_t z = get_angle_value();
        int16_t diff = calculate_angle_diff(target_z, z);

        if (abs(diff) < 2)
        {
            break;
        }

        float derivative = diff - pd.prev_error;
        float output = pd.kp * diff + pd.kd * derivative;
        pd.prev_error = diff;

        uint16_t speed = (uint16_t)fabs(output);
        if (speed < MIN_ROTATION_DUTY_CYCLE)
        {
            speed = MIN_ROTATION_DUTY_CYCLE;
        }
        else if (speed > ROTATION_SPEED)
        {
            speed = ROTATION_SPEED;
        }

        set_all_pwm_channels(speed);
    }

    set_all_pwm_channels(0);
}

/* ==================== ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?6?1?7?1?7?1?7 ==================== */

/**

 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?0?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_forward_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 0);
    tb6612_set_direction(3, 0);
    tb6612_set_direction(4, 0);
    set_all_pwm_channels(speed);
}

/**

 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_back_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 1);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 1);
    set_all_pwm_channels(speed);
}

/**

 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_left_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 1);
    tb6612_set_direction(2, 0);
    tb6612_set_direction(3, 0);
    tb6612_set_direction(4, 1);
    set_all_pwm_channels(speed);
}

/**

 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_right_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 0);
    set_all_pwm_channels(speed);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?0?8
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_rotate_left_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 1);
    tb6612_set_direction(2, 1);
    tb6612_set_direction(3, 0);
    tb6612_set_direction(4, 0);
    set_all_pwm_channels(speed);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?0?8
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_rotate_right_openloop(uint16_t speed)
{
    tb6612_set_direction(1, 0);
    tb6612_set_direction(2, 0);
    tb6612_set_direction(3, 1);
    tb6612_set_direction(4, 1);
    set_all_pwm_channels(speed);
}

/* ==================== ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?3?0?7?1?7?1?7?1?7?1?7?0?6?1?7?1?7?1?7 ==================== */

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?7?1?7?0?1?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_forward_count_close(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    uint16_t z = get_angle_value();
    while (wheel_get_count() < count)
    {
        wheel_go_forward(z, speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_back_count_close(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    uint16_t z = get_angle_value();
    while (wheel_get_count() < count)
    {
        wheel_go_back(z, speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_left_count_close(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    uint16_t z = get_angle_value();
    while (wheel_get_count() < count)
    {
        wheel_go_left(z, speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_right_count_close(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    uint16_t z = get_angle_value();
    while (wheel_get_count() < count)
    {
        wheel_go_right(z, speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?2?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_forward_count_open(uint16_t count, uint16_t speed)
{
    // ?1?7?1?7?1?7?0?9?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
    wheel_begin_go();
    // ?1?7?0?9?1?7?0?1?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?9?0?7?1?7?1?7?0?5
    while (wheel_get_count() < count)
    {
        wheel_go_forward_openloop(speed);
    }
    // ?0?5?0?9?1?7?1?7?1?7
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_back_count_open(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    while (wheel_get_count() < count)
    {
        wheel_go_back_openloop(speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_left_count_open(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    while (wheel_get_count() < count)
    {
        wheel_go_left_openloop(speed);
    }
    set_all_pwm_channels(0);
}

/**
 * @brief ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param count ?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
 * @param speed ?0?7?1?7?1?7?1?7?1?8?1?7
 */
void wheel_go_right_count_open(uint16_t count, uint16_t speed)
{
    wheel_begin_go();
    while (wheel_get_count() < count)
    {
        wheel_go_right_openloop(speed);
    }
    set_all_pwm_channels(0);
}

void wheel_polarity_test(void)
{
    const uint16_t distance = 1000;
    const uint16_t speed = 300;
    wheel_go_forward_openloop(0);
    // ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
    // 1234 ?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?0?8
    set_all_pwm_channels_separate(300, 0, 0, 0);
    delay_ms(500);
    set_all_pwm_channels_separate(0, 300, 0, 0);
    delay_ms(500);
    set_all_pwm_channels_separate(0, 0, 300, 0);
    delay_ms(500);
    set_all_pwm_channels_separate(0, 0, 0, 300);
    delay_ms(500);
    wheel_stop();
    delay_ms(500);

    // ?1?7?1?7?0?8
    wheel_rotate_left_openloop(speed);
    delay_ms(500);

    // ?1?7?1?7?0?8
    wheel_rotate_right_openloop(speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7?0?8
    wheel_go_left_openloop(speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7?0?8
    wheel_go_right_openloop(speed);
    delay_ms(500);

    // ?0?2?1?7?1?7
    wheel_go_forward_openloop(speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7
    wheel_go_back_openloop(speed);
    delay_ms(500);

    // ?1?7?0?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7?1?7
    // ?0?2?1?7?1?7
    wheel_go_forward_count_close(distance, speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7
    wheel_go_back_count_close(distance, speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7
    wheel_go_left_count_close(distance, speed);
    delay_ms(500);

    // ?1?7?1?7?1?7?1?7
    wheel_go_right_count_close(distance, speed);
    delay_ms(500);

    wheel_rotate(90);
    wheel_stop();
    delay_ms(500);

    wheel_rotate(-90);
    wheel_stop();
    delay_ms(500);
}